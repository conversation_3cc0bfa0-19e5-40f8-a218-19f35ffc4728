# Logs
*.log
.DS_Store
*.rdb
/.happypack/
# config etc
.idea/
/config.js
/temp/
# Runtime data
#pids

*.pid
*.seed
*.gz
.project

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Dependency directory
/node_modules/
bower_components/

npm-debug.log*

/cmds/logs/*
/cmds/pids/*

/logs/*
/pids/*

tests/e2e/reports/*

docs/esdoc
dist/
extend/app

# dependencies
/node_modules
/npm-debug.log*
/yarn-error.log
/yarn.lock
pnpm-lock.yaml

# production
/dist

# misc
.DS_Store

.eslintcache

db.sqlite
