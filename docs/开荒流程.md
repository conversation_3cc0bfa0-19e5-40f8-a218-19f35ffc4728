## 开荒流程

### 后端

#### 第一步 - 设计表（类型，注释，结构等）；

例如：

```ts
import { BelongsTo, Column, DataType, Table } from 'sequelize-typescript'
import { BaseModel } from '@/entity/base'
import { Tag } from './tag'

// 表名要求简洁，用下划线分割
@Table({
  tableName: 'demo',
  comment: 'demo'
})
export class Demo extends BaseModel<Demo> {
  @Column({ type: DataType.STRING(100) })
  title: string

  @Column({ type: DataType.DATE })
  birthday: Date

  // 设置外键字段并绑定关联模型
  @Column({ type: DataType.STRING(32) })
  tagId: number

  // 设置项目表和标签表的关系
  @BelongsTo(() => Tag, { constraints: false, foreignKey: 'tagId' })
  tag: Tag
}
```

#### 第二步 - 初始化后端的 CloudServer，注册 sequelize（和 redis）；

例如：

```ts
private _cloudServer: CloudServer<typeof ENTITY_MAP>
private get cloudServer() {
  if (this._cloudServer) return this._cloudServer
  this._cloudServer = new CloudServer({
    sequelize: this.sequelize as any,
    registerFunction: register => registerFunction(register),
    logging: true
  })
  return this._cloudServer
}

@Post('/api/cloud')
async cloud() {
  this.ctx.body = await this.cloudServer.execKoa({
    body: this.ctx.request.body || {},
    headers: this.ctx.headers,
    session: this.ctx.session,
    userId: null
  })
}
```

#### 第三步（可选） - 编写云函数并注册；

例如：

```ts
import type { CloudFunction } from '@/clouds/type'

export const sum: CloudFunction = (request, context) => {
  const arr = request.params || []

  return arr.reduce((s, v) => s + v, 0)
}

export function registerFunction(register: RegisterFunction) {
  register('appMeta', appMeta)
  register('sum', sum)
}
```


### 前端

#### 第四步 - 生成前端模型类型（如果无自动，需要手动编写）；

例如：

```ts
export interface BaseModel {
  id: string
  createdAt?: Date | string
  updatedAt?: Date | string
  createdBy?: string
  updatedBy?: string
}

export interface Demo extends BaseModel {
  title: string
  tag?: Tag
}

export interface Entity {
  Demo: Demo,
  Tag: Tag,
}
```

#### 第五步 - 初始化前端的 Service/Cloud；

```ts
export const Cloud = createCloudService<Entity>({
  microApp: 'sugo-abi-report-hub',
  baseUrl: '/api/cloud',
  umiRequest,
  entityMap: {
    Demo: 'Demo',
    Tag: 'Tag',
  }
})
```

#### 第六步 - 调用云服务、云函数使用（可选）；

```ts
await Cloud.Tag.findAll({})
```
