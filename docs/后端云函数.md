## sugo-sequelize-cloud 后端云函数

基于 70% 的场景可以在前端进行查询完成，剩下 30% 的场景需要在后端编写云函数完成。

什么是云函数，简单点说后端提供接口和逻辑实现，然后通过函数式调用，在前端进行使用。

哪些场景需要使用云函数：
1. 需要强事务的逻辑；
2. 需要保密、复杂封装的逻辑；
3. 需要文件操作的逻辑，例如 excel 导入，导出；
4. 需要调用第三方服务的逻辑；
5. 需要强计算的逻辑；
6. 需要多表关联，多数据源操作的逻辑；
7. 需要后端环境的逻辑；

### 定义云函数

例如编写一个获取 Ip 的：

```ts
export const getIp: CloudFunction = request => {
  const ip = request.headers['x-forwarded-for'] || request.ip.replace(/(::ffff:)(.*)/, '$2')

  if (Array.isArray(ip)) return ip
  if (ip.indexOf(',') > -1) return ip.split(',').map(i => i.trim())

  return [ip]
}
```

编写完成后注册：

```ts
new CloudServer({
  isDev,
  entityMap: ENTITY_MAP, // 关键
  generateId: generateId,
  sequelize: this.defaultDataSource, // 关键
  registerFunction: register => {
    register('getIp', getIp)
  },
  logging: isDev
})
```

注册完成后即可前端调用：

```js
Service.$execute('getId', {}) // ['127.0.0.1']
```

### 入参与上下文
参考这个 Typescript 类型；

```ts
/** 云函数 */
export interface CloudFunction<Params = any, Ret = any> {
  (
    request: {
      headers?: Request['headers']
      session?: Record<string, any>
      cookies?: any
      state?: Record<string, any>
      userId?: string
      params: Params
    },
    ctx: {
      db: Entitys,
      logger: typeof console
      generateId: (num?: number) => string
      isDev: boolean
      /** 可以在内部运行某云函数 */
      $execute:
      (<T>(name: string, params: T) => any) &
      (<T>(namespace: string, name: string, params: T) => any)
      env: Record<start, any>
      sequelize: Sequelize
      redis: Redis
      redisOptions?: {
        prefix?: string
      }
      config: Record<string, any>
    }
  ): Ret | Promise<Ret>
}
```

示例：

```ts
export const getIp: CloudFunction = (request, context) => {
  const { } = request.params
  const { db, sequelize, redis, logger } = context
}
```

### 编写事务
注意 sequelize 是 sequelize 实例，db 里是 model 实例。

```js
export const getIp: CloudFunction = async (request, context) => {
  const { db, sequelize, redis, logger } = context

  await sequelize.transaction(async transaction => {
    await db.Tag.create({ name: 'abc' }, { transaction })
    await db.Template.update({ name: 'aaa' }, { where: { id: 'aaa' }, transaction })
    await redis.set('aaaa')
  })
}
```

### 注意

1. redis 的 key 在云函数里不会自动拼接，在前端里会自动拼接，例如，配置

```js
redisOptions: {
  prefix: 'abi-cloud-server::'
}
```

前端查询：

```ts
Service.$redis.get('aaa') // 实际上查的是 abi-cloud-server::
```

如果在云函数里，则是无修饰：

```ts
export const getIp: CloudFunction = async (request, context) => {
  const { redis, redisOptions: { prefix } } = context

  redis.get('aaa') // 实际上查的是就是 aaa
  redis.get(`${prefix}aaa`) // 需要手动补全
}
```
