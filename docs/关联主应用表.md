
## sugo-sequelize-cloud 关联主应用/其他应用表
v2.9.0+ 的版本新增，使用 relations 字段即可关联查询主应用/其他应用的表。

例如，子应用的查询只有 createdBy 字段，需要带出用户的其他信息，这时候就可以用到 relations 字段。

### 后端配置
主要配置 dbConfig，如果只是配置 mainAppOrigin，会自动读取主应用的数据库名称、模型名称和表名称。

```js
{
  ...,
  dbConfig: {
    /**
     * 主应用的服务地址，设置了才能使用自动关联用户
     *
     * 类似 http://192.168.0.224:8000
     */
    mainAppOrigin?: 'http://192.168.0.224:8000'
  }
}
```

也可以设置 keyMap 设置关联映射表关系（**读取的优先度最高**）：

```js
{
  ...,
  dbConfig: {
    keyMap: {
      indice: { dbName: 'sugo-indices', tableName: 'sugo_indice_base' }
    }
  }
}
```

如果已知库和表，也可以先配置 mainAppDbConfig：

```js
{
  ...,
  dbConfig: {
    mainAppDbConfig: {
      database: '219_astro_dev',
      tableMap: {
        SugoUser: { tableName: 'sugo_user' }
      }
    }
  }
}
```

设置好后，如果 relations 的 tableName 有匹配的，那么会应对上。

### 前端查询
relations 支持关联多张表，如果是同一个表，多个字段映射，就写在 fieldMapping 里面即可，fieldMapping 就是可以映射多个字段的。

relations 会作为补充的字段，添加到 data 里面。

```js
await Cloud.Demo.findAll({
  /**
   * 主应用/其他应用关联表查询
   *
   * @example
   * relations: {
   *   tableName: 'SugoUser',
   *   fieldMapping: {
   *     createdBy: {
   *       fieldName: 'createdUser',
   *       attributes: ['id', 'username', 'first_name']
   *     }
   *   }
   * }
   */
  relations: {
    tableName: 'SugoUser',
    fieldMapping: {
      createdUser: {
        sourceName: 'createdBy',
        attributes: ['id', 'username', 'first_name']
      },
      updatedUser: {
        sourceName: 'updatedBy',
        attributes: ['id', 'username', 'first_name']
      }
    }
  }
})

// 输出
[{
  id: 'xxx',
  createdBy: 'aaa',
  createdUser: {
    id: 'aaa',
    username: 'test',
    firstName: '哈哈哈'
  },
  updatedUser: {
    id: 'bbb',
    username: 'ddd',
    firstName: '对对对'
  }
}]
```

### 关联其他子应用表

默认是关联主应用的，也可以关联其他应用的表。

你的 keyMap 这样配置

```js
{
  ...,
  dbConfig: {
    keyMap: {
      indiceTable: { dbName: 'sugo-indices', tableName: 'sugo_indice_base' }
    }
  }
}
```

使用 keyMap 的配置，例如指定指标库的：

```js
relations: {
  indiceTable: 'indiceTable' // -> FROM `sugo-indices`.`sugo_indice_base`
  // 如果 tableName 里面有 . 时，会自动解析成 dbName 和 tableName
  // tableName: '219_indices_dev.indices',
  fieldMapping: {
    indice: {
      sourceName: 'indiceId',
      attributes: ['id', 'name', 'code']
    }
  }
}
```


### 注意事项
查询时，源字段必须带出来才行，

例如，下面就是一个错误示范，createdBy 并没有查询出来，就关联不到了。需要改成：`attributes: ['id', 'title', 'createdBy']`

```js
// 错误示范
await Cloud.Demo.findAll({
  attributes: ['id', 'title'],
  relations: {
    tableName: 'SugoUser',
    fieldMapping: {
      createdUser: {
        sourceName: 'createdBy',
        attributes: ['id', 'username', 'first_name']
      }
    }
  }
})
```
