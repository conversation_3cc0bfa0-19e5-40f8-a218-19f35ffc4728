
### 应用间接口服务调用
何为应用间服务调用？如有 A、B、C、D 应用，在前端可以用过 AppService 进行应用之间的接口调用。


#### 在前端调用
会自动填充 microApp，无需填入。

```js
import { AppService } from '@sugo/sequelize-cloud/client'

const res = await AppService.sugoMainApp.get('/user/info', {})
```

#### 在后端调用
默认需要提供 cookie，写入 sugoAuth 即可，会自动填充 headers.cookie，或者你手动填写 headers 也行。

```js
import { AppService } from '@sugo/sequelize-cloud/server'

const res = await AppService.sugoTotalMut.post('/xxx', { data: { ... }, sugoAuth: 'cookie' })
```

#### 支持免登录
需要提供 userId，那么会按这个用户进行自动登录。

```js
import { AppService } from '@sugo/sequelize-cloud/server'

const res = await AppService.sugoTotalMut.post('/xxx', { data: { ... }, sugoLogin: 'userId' })
```

#### 主应用注册服务

```js
import { Consul } from '@sugo/sequelize-cloud/server'

// 服务调用
router.post('/app/call-app-services', async ctx => {
  const { path } = ctx.request.body

  try {
    await Consul.koaExecuteAppService(ctx, {
      redisGet,
      redisSetExpire,
      getUserForSessionStorage,
      EXPIRE_TIME,
      microFrontendApps: conf.microFrontendApps || {}
    })
  } catch (err) {
    console.error('调用错误，请检查 path: ', path, '  和参数是否正确，或者是否认证')
    console.error(err)
    ctx.status = 500
    ctx.body = {
      code: -1,
      error: err?.data || err.message
    }
  }
})
```
