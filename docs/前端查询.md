
## sugo-sequelize-cloud 前端查询

### 列表查询

注意：
- 用 findAll/findAndCountAll 一定要有意识的设置 limit，不然全量查出来对数据库压力很大。
- 前端自动限制了 limit 最大为 1000，超出请申请开放或自行进行分批查询。

```ts
Service.Post.findAll({
  limit: 10
})
```

### 单个查询

```ts
Service.Post.findOne({
  where: { title: { $like: '%哈哈%' } }
})
Service.Post.findByPk('aaa')
```

### 创建/编辑/删除

新增：

```ts
Service.Post.create({
  title: '666'
})
```

更新：

```ts
Service.Post.update({
  title: '哈哈哈'
}, {
  where: { id: 'aa' }
})

// +1 原子更新
Service.Post.update({
  order: "literal('`order` + 1')" // +1
}, {
  where: { id: 'aa' }
})
```

删除：

```ts
Service.Post.delete({
  where: { id: 'aaa' }
})
```

批量创建：

```ts
Service.Post.bulkCreate([
  { title: 'aaa' },
  { title: 'bbb' },
], {})
```

批量更新（带 id 为更新，请确保 id 存在，不然会创建）：

```ts
Service.Post.bulkCreate([
  { id: 'a', title: 'aaa' },
  { id: 'a', title: 'bbb' },
], {
  updateOnDuplicate: ['title'] // 指定需要更新的字段，不能指定 id
})
```

### 关联查询

```ts
// 写法 1
Service.Post.findAll({
  limit: 10,
  include: 'Tag' // 关联 tag
})
// 写法 2
Service.Post.findAll({
  limit: 10,
  include: ['Tag'] // 关联 tag
})
// 写法 3
Service.Post.findAll({
  limit: 10,
  include: {
    model: 'Tag',
    required: false // 空关联也查询出来
  }
})
Service.Post.findAll({
  limit: 10,
  include: {
    model: 'Tag',
    required: false   // 空关联也查询出来
    include: [{       // 二级关联只能，写成数组 + 对象形式
      model: 'User',  // tag 再关联 user
    }]
  }
})
```

### 使用 col、fn、where、literal 函数

```ts
Service.Report.findAll({
  attributes: [['fn("SUM", col("status"))', 'sum']]
})
```

### 聚合查询

```ts
Service.Post.count({
  groupBy: ['tagId']
})
```

聚合后进行二次筛选：

```ts
Service.Post.count({
  attributes: [
    ["fn('MONTH', col('created_at'))", "month"],
    ["fn('COUNT', '*')", 'count']
  ],
  groupBy: ["fn('MONTH', col('created_at'))"],
  having: {
    month: {
      $get: 4 // 聚合后，过滤出 month 大于 4 的
    }
  }
})
```

### 全文搜索

```ts
Service.Post.findAll({
  limit: 10,
  where: `literal(MATCH(title, content) AGAINST('${keyword}' IN BOOLEAN MODE))`
})
```

### JSON 查询


sequelize 本来就支持 json 查询了，例如有 JSON 字段为：

```js
config: { key: string, list: { name: string }[] }
```

要查询 key 为 'aaa' 的：

```js
Service.Tag.findAll({
  where: {
    'config.key': 'aaa',
  },
})
// 或者用 $contains
Service.Tag.findAll({
  where: {
    config: { // 查询 config.sign 为 9eoxqd 的
      $contains: { sign: '9eoxqd' }
    }
  }
})
```

（仅限 PG）查询数组也如此（$[]）：

```js
Service.Tag.findAll({
  where: {
    'config.list.$[].name': 'bbb',
  },
})
```

#### JSON 数组操作符
也支持 mysql 哦

- $contains: 过滤出包含指定元素的 JSON 数组（相当于 MySQL 中的 LIKE 操作符）。
- $overlap: 过滤出与指定数组存在交集的 JSON 数组。
- $contained: 过滤出被指定数组所包含的 JSON 数组（相当于 MySQL 中的 IN 操作符）。
- $adjacent: 过滤出与指定数组严格相邻的 JSON 数组（相当于 MySQL 中的 _ 通配符，表示单个字符）。
- $strictLeft: 过滤出被指定数组所包含的 JSON 数组的左部分。
- $strictRight: 过滤出被指定数组所包含的 JSON 数组的右部分。
- $noExtendRight: 过滤出被指定数组所包含的 JSON 数组，但是排除右侧多余的元素。
- $noExtendLeft: 过滤出被指定数组所包含的 JSON 数组，但是排除左侧多余的元素。

```js
Service.Tag.findAll({
  where: {
    $contains: ['bbb']
  }
})
```

TODO: 如果是 mysql，这些操作符会转成 JSON 函数去处理，例如 JSON_CONTAINS。

### 批量查询
批量查询目前不支持 $execute 和 redis。

```ts
// 注意需要返回一个数组，并且需要用参数的调用
const [tags, domes] = await Service.$batch(S => [
  S.Tag.findAll({}),
  S.Demo.findAll({}),
])
```

### 查询参数拦截
由于请求用的是 umi-request，因此在 request 层上做即可。

相关实现的场景：
- 查询时，自动为 where 添加 userId
- 创建/更新时，自动为 data 添加 userId
- 前置条件操作，查询前验证 token 是否已经存在，可在这里抛出异常（路由守卫）

示例：

```ts
const modify = (body: {
  modelName: string
  functionName: string
  namespace?: string
  params: Params
  data?: Record<string, any> | Record<string, any>[]
}) => {
  const { modelName, functionName, params, data } = body
  const userId = getUserId()

  // 查询时，自动添加当前用户
  if (modelName === 'Post' && _.startsWith(functionName, 'find')) {
    params.where = { ...params.where, createdBy: userId }
  }

  if (modelName === 'Post' && functionName === 'create') {
    body.data = { ...data, createdBy: userId }
  }

  return data
}

umiRequest.interceptors.request.use((url, options) => {
  // 在这里可以对请求参数进行处理，例如添加公共参数、转换参数格式等
  let body = _.cloneDeep(options.data)
  body = _.isArray(body) ? body.map(modify) : modify(body)

  // 返回处理后的请求参数
  return {
    url,
    options: { ...options, data: body }
  }
})

export const Service = createCloudService<Entity>({
  microApp: 'sugo-abi-report-hub',
  baseUrl: '/api/cloud', // 可以包含域名 http://abc.com/api/cloud
  umiRequest, // umi-request
  entityMap: {
    Tag: 'Tag', // value 可以随便写一个，因为是根据 key 识别的
  }
})
```

### 查询已经删除了的数据

```ts
Service.Post.findAll({
  paranoid: false, // 这个设置为 false
  where: { id: 'aaa' }
})
```

### 取消前端请求
在 v2.1.0 以上版本支持，请看下面示例：

```ts
try {
  // 使用 cancelId 绑定
  Service.Post.findAll({ where: { id: 'aaa' }, cancelId: '666' })
  Service.$execute('getApp', {}, { cancelId: 'aaa' })

  // 在请求响应完成前有效
  Service.$cancel('666') // 取消单个
  Service.$cancel(id => /^6/.test(id)) // 批量取消
} catch (err) {
  console.error(err)
}
```

注意：取消后，原来的代码会拋一个 Cancel 异常。
