## sugo-sequelize-cloud 跨库查询

### 后端配置
后端最好配置映射表，不然有被利用查询的风险。

```js
new CloudServer({
  generateId: genId,
  sequelize: this.sequelize,
  logging: isDev,
  dbConfig: {
    // 开启了就可以查询任意库，但是风险很大，不建议开启
    allowAnyDbQuery: true,
    allowOnlyGenerateSql: true,
    // 这里配置映射表
    keyMap: {
      'abi.tag': { dbName: 'abi-dev-1', tableName: 'tag' }
    }
  }
})
```

### 前端查询

```js
Service
  .$use('abi.tag')
  .findAll({
    where: { title: { $like: '西瓜' } }
  })
```

如果开启了 allowAnyDbQuery，就可以这样查询：

```js
Service
  .$use({ dbName: 'abi-dev-1', tableName: 'tag' })
  .findAll({
    where: { title: { $like: '西瓜' } }
  })
```
