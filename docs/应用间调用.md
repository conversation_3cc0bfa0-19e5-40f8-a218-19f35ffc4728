## sugo-sequelize-cloud 应用间服务调用

假设有两个子应用，支持子子应用之间的服务调用。
前提：
- A、B 应用初始化了 client 和 server。
- A 应用的服务地址是 127.0.0.1:3000/a/api/cloud
- B 应用的服务地址是 127.0.0.1:3001/b/api/cloud

在 A 应用的 client 里调用 B 应用的服务（主要是根据类型进行创建）：

```ts
// a/client/src/service/index.ts
export const BAppService = createCloudService<BAppEntity>({
  microApp: 'sugo-app-b',
  baseUrl: 'http://127.0.0.1:3001/b/api/cloud', // 指向 b 的服务
  umiRequest, // umi-request
  entityMap: { User: 'User' } // 声明允许调用的 model
})
```

在 A 应用的 server 里调用 B 应用的服务（跟上面一样），假设这是在 A 的云函数里：

```ts
// a/server/src/clouds/functions/test.ts
// 先找个地方初始化 BAppService
export const BAppService = createCloudService<BAppEntity>({
  microApp: 'sugo-app-b',
  baseUrl: 'http://127.0.0.1:3001/b/api/cloud', // 指向 b 的服务
  umiRequest, // umi-request
  entityMap: { User: 'User' } // 声明允许调用的 model
})

// 编写云函数
import type { CloudFunction } from '@/clouds/type'
// 在 A 里调用 B 的服务
export const test: CloudFunction = (request, context) => {
  const users = await BAppService.User.findAll({})
  // 最终会转换成  umiRequest.post({ ... })
}
```
