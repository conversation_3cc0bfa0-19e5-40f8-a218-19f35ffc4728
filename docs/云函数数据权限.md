
## 云函数资源权限使用
在 **@sugo/sequelize-cloud v2.5.10+** 支持，注意版本，对应的是 **“资源授权”** 模块的数据。

注意：
- 后端需要配置 redis，如果上下文有 session 传递下来，能直接拿到权限数据那最好，如果没有会自动从 redis 里获取。

```ts
cloudServer.setOptions({
  sequelize: this.db.sequelize,
  redis: this.redis, // 提供 redis
  headers: ctx.headers, // 确保提供 headers
  session: ctx.session, // 注入用户信息到 session，云函数 request.session
})
```

### 零. 权限流程

**后端：**
1. 在资源授权页面里面进行配置，数据会存在 mysql 的 sugo_data_permission 表里面，主要按照 userId、roleId 进行存。
2. 用户登录后，会一次从 mysql 里查询权限数据，然后写到 redis 里面，此时可以在 ctx.session.dataPermission 里面拿到数据（在主应用的）。

**前端：**
1. 在子应用前端里面，通过**云函数 $fn.getDataPermission** 获取权限数据。
2. 前端拿到 `ids` 后，可传给**查询列表的函数**做 where 过滤，同时也能拿到 configMap 的数据，做相关权限控制。

---

### 1. 前端查询使用权限
使用 `usePermission: true` 开启权限限制，如果没权限则返回空数组。
云函数获取看第三点。

```ts
Cloud.IndicesBaseModel.findAll({
  attributes: ['id', 'name'],
  where: {
    name: { $like: '销售% }
  },
  limit: 10,
  usePermission: true // 权限
}
```

**TODO：**
- 新的权限查询，基于 mysql 关联条件查询，使用 `usePermission: 'new'`，`true` 还是走 redis。
- 新的权限查询，需要完整配置 dataPermissionConfig.keyMap，否则判断将出错。
- **云函数 $fn.getDataPermission** 还是返回 redis 的。
- 新的权限查询，在 v2.10.0 得到初步支持，在后续的版本修复问题，估计在 v2.10.5 之前会稳定。

**注意：**
- findAll、findOne、findAllAndCount 在没权限时会返回 []；
- update、findOrCreate、upsert、delete、findByPk、updateByPk 在没权限时会返回 null 或 [0] 表示更新失败；
- success 会为 true，message 会有说明无权限；
  ```json
  {
    "success": true,
    "result": [],
    "message": "无数据权限"
  }
  ```
- **此功能需要在后端设置映射关系：**
  ```js
  dataPermissionConfig: {
    enable: true,
    /**
     * Model 名称与权限 type 的直接映射
     * 例如：{
     *   IndicesBaseModel: 'indicesRecord',
     *   // 默认关联的是 id，也可以指定关系
     *   ThemeSliceModel: { type: 'abiThemeAnalysis', fieldName: 'themeId'  },
     * }
     */
    keyMap: {
      // 驼峰指定转成下划线
      IndicesBaseModel: 'indicesRecord',
      // 指定 type 和 admin type
      ThemeSliceModel: {
        type: 'abi_theme_analysis',
        admin: 'abi_theme_analysis_admin',
        targetFieldName: 'themeId'
      }, // 关联表，指定主表的权限
    }
  }
  ```

### 2. 自定义云函数使用权限（后端）
在 context 里有权限的代理对象。dataPermission 有三个函数：
- get(key: string | string[]): any | any[]
- getAll()
- getKeys()

```ts
const getUserList: CloudFunction = async (request, context) => {
  const { dataPermission } = context // 重点

  // key 不要写错，写错获取到的是 undefiend
  const ids = await dataPermission.get('indicesRecord') // ['a', 'b']
  // 自行塞到 where 里面
  const where = { id: [Op.in]: ids }
}
```

**使用示例：**

```ts
const [indicesAdmin, indicesRecord] = await dataPermission.get(['indicesAdmin', 'indicesRecord'])
// 过滤权限，修改入参
if (!indicesAdmin && indicesRecord) {
  if (_.isEmpty(request.params.indiceIds)) {
    request.params.indiceIds = indicesRecord
  } else {
    request.params.indiceIds = _.filter(request.params.indiceIds, id => _.includes(indicesRecord, id))
  }
}
```

### 3. 获取权限的云函数（前端）
内置了一个获取权限的云函数 getDataPermission，只能获取自己的权限。

```ts
// 返回一个对象
// 写法一
const res = await clientCloud.$fn.getDataPermission({
  type: 'indicesRecord'
})
// 写法二
const res = await clientCloud.$fn.getDataPermission({
  type: ['indicesRecord', 'indicesGroup']
})
// 写法三
const res = await clientCloud.$fn.getDataPermission({
  type: 'indices*' // 最好不要空，支持数组 ['xxx', 'yyy']
})
// 写法四，返回全部，不建议写法
const res = await clientCloud.$fn.getDataPermission({ })

// res 是一个对象
const ids = res.indicesRecord || []
```


### 4. 返回数据结构

部分结构为：
- `boolean`
- `string[]`
- `{ id: string, isSelectAll: boolean, [key: string]: any }[]`
- `Record<string, any>`
- `xxxConfigMap: {}`

**注意：**目前主要存的是以 id 为主，配置一般在 xxxConfigMap 里面，key-value 结果，key 就是 id。

```json
// getDataPermission 云函数返回
{
  "success": true,
  "result": {
    "isAdmin": true, // 这个为 true 表示是超管

    "indicesAdmin": true,
    "indicesRecord": [],
    "indicesRecordConfigMap": {},           // 配置
    "indicesGroup": [],
    "indicesSubject": [],
    "indicesDimension": [],
    "indicesDimensionMembers": [],
    "indicesOrganization": [],

    "connectAdmin": true,
    "connectOrigin": [],
    "connectOriginConfigMap": {},
    "connectDb": [],
    "connectTable": [],
    "connectTableConfigMap": {},
    "connectField": [],
    "connectRowConfig": [],

    "modelAdmin": true,
    "modelGroup": [],
    "modelRecord": [],
    "modelRecordConfigMap": {},
    "modelField": [],
    "modelRow": {},

    "analysisIndiceAdmin": true,
    "analysisIndice": [],
    "analysisIndiceConfigMap": {},
    "analysisIndiceGroup": [],

    "taskProjectAdmin": true,
    "taskProject": [],
    "taskProjectConfigMap": {},

    "dashboardsAdmin": true,
    "dashboards": [],
    "dashboardsGroup": [],

    "abiProject": [],
    "abiProjectConfigMap": {},
    "abiProjectAdmin": true,

    "abiThemeAnalysis": [],
    "abiThemeAnalysisConfigMap": {},
    "abiThemeAnalysisGroup": [],
    "abiThemeAnalysisAdmin": true,

    "tagAppProject": [],
    "tagAppProjectConfigMap": {},
    "tagAppGroup": [],
    "tagApp": [],
    "tagAppConfigMap": {},
    "tagAppAdmin": true
  }
}
```

