{"name": "@sugo/sequelize-cloud", "version": "2.13.3", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "@sugo/sequelize-cloud", "version": "2.13.3", "license": "ISC", "dependencies": {"cookie": "^0.5.0", "is-json": "^2.0.1", "lodash": "^4.17.21", "lz-string": "^1.5.0", "nanoid": "^3.3.4"}, "devDependencies": {"@babel/runtime": "^7.22.15", "@sequelize/sqlite3": "^7.0.0-alpha.41", "@types/jest": "^29.5.3", "@types/lodash": "^4.14.195", "@types/node": "^20.4.1", "jest": "^29.6.1", "sequelize": "^6.32.1", "sqlite3": "^5.1.6", "ts-jest": "^29.1.1", "typescript": "^5.1.6"}}, "node_modules/@ampproject/remapping": {"version": "2.2.1", "resolved": "http://*************:4873/@ampproject/remapping/-/remapping-2.2.1.tgz", "integrity": "sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==", "dev": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/code-frame/-/code-frame-7.22.5.tgz", "integrity": "sha512-Xmwn266vad+6DAqEB2A6V/CcZVp62BbwVmcOJc2RPuwih1kw02TjQvWVWlcKGbBPd+8/0V5DEkOcizRGYsspYQ==", "dev": true, "dependencies": {"@babel/highlight": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.22.6", "resolved": "http://*************:4873/@babel/compat-data/-/compat-data-7.22.6.tgz", "integrity": "sha512-29tfsWTq2Ftu7MXmimyC0C5FDZv5DYxOZkh3XD3+QW4V/BYuv/LyEsjj3c0hqedEaDt6DBfDvexMKU8YevdqFg==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.22.8", "resolved": "http://*************:4873/@babel/core/-/core-7.22.8.tgz", "integrity": "sha512-75+KxFB4CZqYRXjx4NlR4J7yGvKumBuZTmV4NV6v09dVXXkuYVYLT68N6HCzLvfJ+fWCxQsntNzKwwIXL4bHnw==", "dev": true, "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.22.5", "@babel/generator": "^7.22.7", "@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-module-transforms": "^7.22.5", "@babel/helpers": "^7.22.6", "@babel/parser": "^7.22.7", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.8", "@babel/types": "^7.22.5", "@nicolo-ribaudo/semver-v6": "^6.3.3", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.2"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/convert-source-map": {"version": "1.9.0", "resolved": "http://*************:4873/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==", "dev": true}, "node_modules/@babel/generator": {"version": "7.22.7", "resolved": "http://*************:4873/@babel/generator/-/generator-7.22.7.tgz", "integrity": "sha512-p+jPjMG+SI8yvIaxGgeW24u7q9+5+TGpZh8/CuB7RhBKd7RCy8FayNEFNNKrNK/eUcY/4ExQqLmyrvBXKsIcwQ==", "dev": true, "dependencies": {"@babel/types": "^7.22.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.22.6", "resolved": "http://*************:4873/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.6.tgz", "integrity": "sha512-534sYEqWD9VfUm3IPn2SLcH4Q3P86XL+QvqdC7ZsFrzyyPF3T4XGiVghF6PTYNdWg6pXuoqXxNQAhbYeEInTzA==", "dev": true, "dependencies": {"@babel/compat-data": "^7.22.6", "@babel/helper-validator-option": "^7.22.5", "@nicolo-ribaudo/semver-v6": "^6.3.3", "browserslist": "^4.21.9", "lru-cache": "^5.1.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache": {"version": "5.1.1", "resolved": "http://*************:4873/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "dependencies": {"yallist": "^3.0.2"}}, "node_modules/@babel/helper-compilation-targets/node_modules/yallist": {"version": "3.1.1", "resolved": "http://*************:4873/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true}, "node_modules/@babel/helper-environment-visitor": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.5.tgz", "integrity": "sha512-XGmhECfVA/5sAt+H+xpSg0mfrHq6FzNr9Oxh7PSEBBRUb/mL7Kz3NICXb194rCqAEdxkhPT1a88teizAFyvk8Q==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-function-name": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-function-name/-/helper-function-name-7.22.5.tgz", "integrity": "sha512-wtHSq6jMRE3uF2otvfuD3DIvVhOsSNshQl0Qrd7qC9oQJzHvOL4qQXlQn2916+CXGywIjpGuIkoyZRRxHPiNQQ==", "dev": true, "dependencies": {"@babel/template": "^7.22.5", "@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-hoist-variables": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz", "integrity": "sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==", "dev": true, "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-module-imports/-/helper-module-imports-7.22.5.tgz", "integrity": "sha512-8Dl6+HD/cKifutF5qGd/8ZJi84QeAKh+CEe1sBzz8UayBBGg1dAIJrdHOcOM5b2MpzWL2yuotJTtGjETq0qjXg==", "dev": true, "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-module-transforms/-/helper-module-transforms-7.22.5.tgz", "integrity": "sha512-+hGKDt/Ze8GFExiVHno/2dvG5IdstpzCq0y4Qc9OJ25D4q3pKfiIP/4Vp3/JvhDkLKsDK2api3q3fpIgiIF5bw==", "dev": true, "dependencies": {"@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/helper-simple-access": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-plugin-utils/-/helper-plugin-utils-7.22.5.tgz", "integrity": "sha512-uLls06UVKgFG9QD4OeFYLEGteMIAa5kpTPcFL28yuCIIzsf6ZyKZMllKVOCZFhiZ5ptnwX4mtKdWCBE/uT4amg==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-simple-access": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz", "integrity": "sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==", "dev": true, "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-split-export-declaration": {"version": "7.22.6", "resolved": "http://*************:4873/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz", "integrity": "sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==", "dev": true, "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz", "integrity": "sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz", "integrity": "sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-validator-option/-/helper-validator-option-7.22.5.tgz", "integrity": "sha512-R3oB6xlIVKUnxNUxbmgq7pKjxpru24zlimpE8WK47fACIlM0II/Hm1RS8IaOI7NgCr6LNS+jl5l75m20npAziw==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.22.6", "resolved": "http://*************:4873/@babel/helpers/-/helpers-7.22.6.tgz", "integrity": "sha512-YjDs6y/fVOYFV8hAf1rxd1QvR9wJe1pDBZ2AREKq/SDayfPzgk0PBnVuTCE5X1acEpMMNOVUqoe+OwiZGJ+OaA==", "dev": true, "dependencies": {"@babel/template": "^7.22.5", "@babel/traverse": "^7.22.6", "@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/highlight/-/highlight-7.22.5.tgz", "integrity": "sha512-BSKlD1hgnedS5XRnGOljZawtag7H1yPfQp0tdNJCHoH6AZ+Pcm9VvkrK59/Yy593Ypg0zMxH2BxD1VPYUQ7UIw==", "dev": true, "dependencies": {"@babel/helper-validator-identifier": "^7.22.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight/node_modules/ansi-styles": {"version": "3.2.1", "resolved": "http://*************:4873/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/chalk": {"version": "2.4.2", "resolved": "http://*************:4873/chalk/-/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/color-convert": {"version": "1.9.3", "resolved": "http://*************:4873/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "dependencies": {"color-name": "1.1.3"}}, "node_modules/@babel/highlight/node_modules/color-name": {"version": "1.1.3", "resolved": "http://*************:4873/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "node_modules/@babel/highlight/node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "http://*************:4873/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/@babel/highlight/node_modules/has-flag": {"version": "3.0.0", "resolved": "http://*************:4873/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/supports-color": {"version": "5.5.0", "resolved": "http://*************:4873/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/parser": {"version": "7.22.7", "resolved": "http://*************:4873/@babel/parser/-/parser-7.22.7.tgz", "integrity": "sha512-7NF8pOkHP5o2vpmGgNGcfAeCvOYhGLyA3Z4eBQkT1RJlWu47n63bCs93QfJ2hIAFCil7L5P2IWhs1oToVgrL0Q==", "dev": true, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "http://*************:4873/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "integrity": "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "http://*************:4873/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "resolved": "http://*************:4873/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "integrity": "sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.22.5.tgz", "integrity": "sha512-gvyP4hZrgrs/wWMaocvxZ44Hw0b3W8Pe+cMxc8V1ULQ07oh8VNbIRaoD1LRZVTvD+0nieDKjfgKg89sD7rrKrg==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "http://*************:4873/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha1-ypHvRjA1MESLkGZSusLp/plB9pk=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "http://*************:4873/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "http://*************:4873/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.22.5.tgz", "integrity": "sha512-1mS2o03i7t1c6VzH6fdQ3OA8tcEIxwG18zIPRp+UY1Ihv6W+XZzBCVxExF9upussPXJ0xE9XRHwMoNs1ep/nRQ==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.24.7", "resolved": "http://*************:4873/@babel/runtime/-/runtime-7.24.7.tgz", "integrity": "sha512-UwgBRMjJP+xv857DCngvqXI3Iq6J4v0wXmwc6sapg+zyhbwmQX67LUEFrkK5tbyJ30jGuG3ZvWpBiB9LCy1kWw==", "dev": true, "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/template/-/template-7.22.5.tgz", "integrity": "sha512-X7yV7eiwAxdj9k94NEylvbVHLiVG1nvzCV2EAowhxLTwODV1jl9UzZ48leOC0sH7OnuHrIkllaBgneUykIcZaw==", "dev": true, "dependencies": {"@babel/code-frame": "^7.22.5", "@babel/parser": "^7.22.5", "@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.22.8", "resolved": "http://*************:4873/@babel/traverse/-/traverse-7.22.8.tgz", "integrity": "sha512-y6LPR+wpM2I3qJrsheCTwhIinzkETbplIgPBbwvqPKc+uljeA5gP+3nP8irdYt1mjQaDnlIcG+dw8OjAco4GXw==", "dev": true, "dependencies": {"@babel/code-frame": "^7.22.5", "@babel/generator": "^7.22.7", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/parser": "^7.22.7", "@babel/types": "^7.22.5", "debug": "^4.1.0", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/types/-/types-7.22.5.tgz", "integrity": "sha512-zo3MIHGOkPOfoRXitsgHLjEXmlDaD/5KU1Uzuc9GNiZPhSqVxVRtxuPaSBZDsYZ9qV88AjtMtWW7ww98loJ9KA==", "dev": true, "dependencies": {"@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "to-fast-properties": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bcoe/v8-coverage": {"version": "0.2.3", "resolved": "http://*************:4873/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "integrity": "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==", "dev": true}, "node_modules/@gar/promisify": {"version": "1.1.3", "resolved": "http://*************:4873/@gar/promisify/-/promisify-1.1.3.tgz", "integrity": "sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==", "dev": true, "optional": true}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "resolved": "http://*************:4873/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "integrity": "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==", "dev": true, "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "resolved": "http://*************:4873/@istanbuljs/schema/-/schema-0.1.3.tgz", "integrity": "sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/@jest/console": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/console/-/console-29.6.1.tgz", "integrity": "sha512-Aj772AYgwTSr5w8qnyoJ0eDYvN6bMsH3ORH1ivMotrInHLKdUz6BDlaEXHdM6kODaBIkNIyQGzsMvRdOv7VG7Q==", "dev": true, "dependencies": {"@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^29.6.1", "jest-util": "^29.6.1", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/core": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/core/-/core-29.6.1.tgz", "integrity": "sha512-CcowHypRSm5oYQ1obz1wfvkjZZ2qoQlrKKvlfPwh5jUXVU12TWr2qMeH8chLMuTFzHh5a1g2yaqlqDICbr+ukQ==", "dev": true, "dependencies": {"@jest/console": "^29.6.1", "@jest/reporters": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^29.5.0", "jest-config": "^29.6.1", "jest-haste-map": "^29.6.1", "jest-message-util": "^29.6.1", "jest-regex-util": "^29.4.3", "jest-resolve": "^29.6.1", "jest-resolve-dependencies": "^29.6.1", "jest-runner": "^29.6.1", "jest-runtime": "^29.6.1", "jest-snapshot": "^29.6.1", "jest-util": "^29.6.1", "jest-validate": "^29.6.1", "jest-watcher": "^29.6.1", "micromatch": "^4.0.4", "pretty-format": "^29.6.1", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/environment": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/environment/-/environment-29.6.1.tgz", "integrity": "sha512-RMMXx4ws+Gbvw3DfLSuo2cfQlK7IwGbpuEWXCqyYDcqYTI+9Ju3a5hDnXaxjNsa6uKh9PQF2v+qg+RLe63tz5A==", "dev": true, "dependencies": {"@jest/fake-timers": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "jest-mock": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/expect/-/expect-29.6.1.tgz", "integrity": "sha512-N5xlPrAYaRNyFgVf2s9Uyyvr795jnB6rObuPx4QFvNJz8aAjpZUDfO4bh5G/xuplMID8PrnuF1+SfSyDxhsgYg==", "dev": true, "dependencies": {"expect": "^29.6.1", "jest-snapshot": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect-utils": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/expect-utils/-/expect-utils-29.6.1.tgz", "integrity": "sha512-o319vIf5pEMx0LmzSxxkYYxo4wrRLKHq9dP1yJU7FoPTB0LfAKSz8SWD6D/6U3v/O52t9cF5t+MeJiRsfk7zMw==", "dev": true, "dependencies": {"jest-get-type": "^29.4.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/fake-timers": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/fake-timers/-/fake-timers-29.6.1.tgz", "integrity": "sha512-RdgHgbXyosCDMVYmj7lLpUwXA4c69vcNzhrt69dJJdf8azUrpRh3ckFCaTPNjsEeRi27Cig0oKDGxy5j7hOgHg==", "dev": true, "dependencies": {"@jest/types": "^29.6.1", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.6.1", "jest-mock": "^29.6.1", "jest-util": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/globals": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/globals/-/globals-29.6.1.tgz", "integrity": "sha512-2VjpaGy78JY9n9370H8zGRCFbYVWwjY6RdDMhoJHa1sYfwe6XM/azGN0SjY8kk7BOZApIejQ1BFPyH7FPG0w3A==", "dev": true, "dependencies": {"@jest/environment": "^29.6.1", "@jest/expect": "^29.6.1", "@jest/types": "^29.6.1", "jest-mock": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/reporters": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/reporters/-/reporters-29.6.1.tgz", "integrity": "sha512-9zuaI9QKr9JnoZtFQlw4GREQbxgmNYXU6QuWtmuODvk5nvPUeBYapVR/VYMyi2WSx3jXTLJTJji8rN6+Cm4+FA==", "dev": true, "dependencies": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@jridgewell/trace-mapping": "^0.3.18", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^5.1.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-message-util": "^29.6.1", "jest-util": "^29.6.1", "jest-worker": "^29.6.1", "slash": "^3.0.0", "string-length": "^4.0.1", "strip-ansi": "^6.0.0", "v8-to-istanbul": "^9.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/schemas": {"version": "29.6.0", "resolved": "http://*************:4873/@jest/schemas/-/schemas-29.6.0.tgz", "integrity": "sha512-rxLjXyJBTL4LQeJW3aKo0M/+GkCOXsO+8i9Iu7eDb6KwtP65ayoDsitrdPBtujxQ88k4wI2FNYfa6TOGwSn6cQ==", "dev": true, "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/source-map": {"version": "29.6.0", "resolved": "http://*************:4873/@jest/source-map/-/source-map-29.6.0.tgz", "integrity": "sha512-oA+I2SHHQGxDCZpbrsCQSoMLb3Bz547JnM+jUr9qEbuw0vQlWZfpPS7CO9J7XiwKicEz9OFn/IYoLkkiUD7bzA==", "dev": true, "dependencies": {"@jridgewell/trace-mapping": "^0.3.18", "callsites": "^3.0.0", "graceful-fs": "^4.2.9"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-result": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/test-result/-/test-result-29.6.1.tgz", "integrity": "sha512-Ynr13ZRcpX6INak0TPUukU8GWRfm/vAytE3JbJNGAvINySWYdfE7dGZMbk36oVuK4CigpbhMn8eg1dixZ7ZJOw==", "dev": true, "dependencies": {"@jest/console": "^29.6.1", "@jest/types": "^29.6.1", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-sequencer": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/test-sequencer/-/test-sequencer-29.6.1.tgz", "integrity": "sha512-oBkC36PCDf/wb6dWeQIhaviU0l5u6VCsXa119yqdUosYAt7/FbQU2M2UoziO3igj/HBDEgp57ONQ3fm0v9uyyg==", "dev": true, "dependencies": {"@jest/test-result": "^29.6.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/transform": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/transform/-/transform-29.6.1.tgz", "integrity": "sha512-URnTneIU3ZjRSaf906cvf6Hpox3hIeJXRnz3VDSw5/X93gR8ycdfSIEy19FlVx8NFmpN7fe3Gb1xF+NjXaQLWg==", "dev": true, "dependencies": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.1", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "jest-regex-util": "^29.4.3", "jest-util": "^29.6.1", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/types": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/types/-/types-29.6.1.tgz", "integrity": "sha512-tPKQNMPuXgvdOn2/Lg9HNfUvjYVGolt04Hp03f5hAk878uwOLikN+JzeLY0HcVgKgFl9Hs3EIqpu3WX27XNhnw==", "dev": true, "dependencies": {"@jest/schemas": "^29.6.0", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.3", "resolved": "http://*************:4873/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz", "integrity": "sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==", "dev": true, "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "http://*************:4873/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.1.2", "resolved": "http://*************:4873/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.15", "resolved": "http://*************:4873/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "integrity": "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==", "dev": true}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.18", "resolved": "http://*************:4873/@jridgewell/trace-mapping/-/trace-mapping-0.3.18.tgz", "integrity": "sha512-w+niJYzMHdd7USdiH2U6869nqhD2nbfZXND5Yp93qIbEmnDNk7PD48o+YchRVpzMU7M6jVCbenTR7PA1FLQ9pA==", "dev": true, "dependencies": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}}, "node_modules/@jridgewell/trace-mapping/node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.14", "resolved": "http://*************:4873/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "dev": true}, "node_modules/@nicolo-ribaudo/semver-v6": {"version": "6.3.3", "resolved": "http://*************:4873/@nicolo-ribaudo/semver-v6/-/semver-v6-6.3.3.tgz", "integrity": "sha512-3Yc1fUTs69MG/uZbJlLSI3JISMn2UV2rg+1D/vROUqZyh3l6iYHCs7GMp+M40ZD7yOdDbYjJcU1oTJhrc+dGKg==", "dev": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "http://*************:4873/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "http://*************:4873/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "http://*************:4873/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@npmcli/fs": {"version": "1.1.1", "resolved": "http://*************:4873/@npmcli/fs/-/fs-1.1.1.tgz", "integrity": "sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==", "dev": true, "optional": true, "dependencies": {"@gar/promisify": "^1.0.1", "semver": "^7.3.5"}}, "node_modules/@npmcli/move-file": {"version": "1.1.2", "resolved": "http://*************:4873/@npmcli/move-file/-/move-file-1.1.2.tgz", "integrity": "sha1-GoLD43L3yuklPrZtclQ9a4aFxnQ=", "dev": true, "optional": true, "dependencies": {"mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "engines": {"node": ">=10"}}, "node_modules/@sequelize/core": {"version": "7.0.0-alpha.42", "resolved": "http://*************:4873/@sequelize/core/-/core-7.0.0-alpha.42.tgz", "integrity": "sha512-rjj7wxVkOiPIuDzwR5AMIPBfNjFRwVpYUCwQ3j3ovtw8UPM1WSNECC1WKkF3SoX9cKZpDgYuQsDzEIontDo3vg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/sequelize"}], "dependencies": {"@sequelize/utils": "7.0.0-alpha.42", "@types/debug": "^4.1.12", "@types/validator": "^13.11.9", "ansis": "^3.2.0", "bnf-parser": "^3.1.6", "dayjs": "^1.11.10", "debug": "^4.3.4", "dottie": "^2.0.6", "fast-glob": "^3.3.2", "inflection": "^3.0.0", "lodash": "^4.17.21", "retry-as-promised": "^7.0.4", "semver": "^7.3", "sequelize-pool": "^8.0.0", "toposort-class": "^1.0.1", "type-fest": "^4.14.0", "uuid": "^10.0.0", "validator": "^13.11.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@sequelize/core/node_modules/inflection": {"version": "3.0.0", "resolved": "http://*************:4873/inflection/-/inflection-3.0.0.tgz", "integrity": "sha512-1zEJU1l19SgJlmwqsEyFTbScw/tkMHFenUo//Y0i+XEP83gDFdMvPizAD/WGcE+l1ku12PcTVHQhO6g5E0UCMw==", "dev": true, "engines": {"node": ">=18.0.0"}}, "node_modules/@sequelize/core/node_modules/sequelize-pool": {"version": "8.0.0", "resolved": "http://*************:4873/sequelize-pool/-/sequelize-pool-8.0.0.tgz", "integrity": "sha512-xY04c5ctp6lKE4ZoSVo7YJHN5FHVhoYMoH2Z8jWIJG26c9kJSkIjql5HgD9XG5cwJbYMF0Ko2Fhgws20HC90Ug==", "dev": true, "engines": {"node": ">= 10.0.0"}}, "node_modules/@sequelize/core/node_modules/type-fest": {"version": "4.26.1", "resolved": "http://*************:4873/type-fest/-/type-fest-4.26.1.tgz", "integrity": "sha512-yOGpmOAL7CkKe/91I5O3gPICmJNLJ1G4zFYVAsRHg7M64biSnPtRj0WNQt++bRkjYOqjWXrhnUw1utzmVErAdg==", "dev": true, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@sequelize/core/node_modules/uuid": {"version": "10.0.0", "resolved": "http://*************:4873/uuid/-/uuid-10.0.0.tgz", "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==", "dev": true, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@sequelize/sqlite3": {"version": "7.0.0-alpha.42", "resolved": "http://*************:4873/@sequelize/sqlite3/-/sqlite3-7.0.0-alpha.42.tgz", "integrity": "sha512-USYRE1wRK9d4cG8ukD6C4m13e8SoPApQ15qubhJ3yXFFXiABhidKER6f9a4GE8GIhCuZlZkAyG+uws0qqn7qfQ==", "dev": true, "dependencies": {"@sequelize/core": "7.0.0-alpha.42", "@sequelize/utils": "7.0.0-alpha.42", "lodash": "^4.17.21", "sqlite3": "^5.1.7"}}, "node_modules/@sequelize/utils": {"version": "7.0.0-alpha.42", "resolved": "http://*************:4873/@sequelize/utils/-/utils-7.0.0-alpha.42.tgz", "integrity": "sha512-O3iiv2OuolcpotOzEzwThEWIdicasXPAr64lWAuS4SRHzrFVufxiq5iuciKx+Lty0vyGIISqVjJRgkPkuq/2fQ==", "dev": true, "dependencies": {"@types/lodash": "^4.17.0", "lodash": "^4.17.21"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "resolved": "http://*************:4873/@sinclair/typebox/-/typebox-0.27.8.tgz", "integrity": "sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==", "dev": true}, "node_modules/@sinonjs/commons": {"version": "3.0.0", "resolved": "http://*************:4873/@sinonjs/commons/-/commons-3.0.0.tgz", "integrity": "sha512-jXBtWAF4vmdNmZgD5FoKsVLv3rPgDnLgPbU84LIJ3otV44vJlDRokVng5v8NFJdCf/da9legHcKaRuZs4L7faA==", "dev": true, "dependencies": {"type-detect": "4.0.8"}}, "node_modules/@sinonjs/fake-timers": {"version": "10.3.0", "resolved": "http://*************:4873/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "integrity": "sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==", "dev": true, "dependencies": {"@sinonjs/commons": "^3.0.0"}}, "node_modules/@tootallnate/once": {"version": "1.1.2", "resolved": "http://*************:4873/@tootallnate/once/-/once-1.1.2.tgz", "integrity": "sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==", "dev": true, "optional": true, "engines": {"node": ">= 6"}}, "node_modules/@types/babel__core": {"version": "7.20.1", "resolved": "http://*************:4873/@types/babel__core/-/babel__core-7.20.1.tgz", "integrity": "sha512-aACu/U/omhdk15O4Nfb+fHgH/z3QsfQzpnvRZhYhThms83ZnAOZz7zZAWO7mn2yyNQaA4xTO8GLK3uqFU4bYYw==", "dev": true, "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.6.4", "resolved": "http://*************:4873/@types/babel__generator/-/babel__generator-7.6.4.tgz", "integrity": "sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg==", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.1", "resolved": "http://*************:4873/@types/babel__template/-/babel__template-7.4.1.tgz", "integrity": "sha1-PRpI/Z1sDt/Vby/1eNrtSPNsiWk=", "dev": true, "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.1", "resolved": "http://*************:4873/@types/babel__traverse/-/babel__traverse-7.20.1.tgz", "integrity": "sha512-MitHFXnhtgwsGZWtT68URpOvLN4EREih1u3QtQiN4VdAxWKRVvGCSvw/Qth0M0Qq3pJpnGOu5JaM/ydK7OGbqg==", "dev": true, "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/debug": {"version": "4.1.12", "resolved": "http://*************:4873/@types/debug/-/debug-4.1.12.tgz", "integrity": "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==", "dev": true, "dependencies": {"@types/ms": "*"}}, "node_modules/@types/graceful-fs": {"version": "4.1.6", "resolved": "http://*************:4873/@types/graceful-fs/-/graceful-fs-4.1.6.tgz", "integrity": "sha512-Sig0SNORX9fdW+bQuTEovKj3uHcUL6LQKbCrrqb1X7J6/ReAbhCXRAhc+SMejhLELFj2QcyuxmUooZ4bt5ReSw==", "dev": true, "dependencies": {"@types/node": "*"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.4", "resolved": "http://*************:4873/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz", "integrity": "sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==", "dev": true}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.0", "resolved": "http://*************:4873/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "integrity": "sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==", "dev": true, "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.1", "resolved": "http://*************:4873/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz", "integrity": "sha1-kVP+mLuivVZaY63ZQ21vDX+EaP8=", "dev": true, "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/jest": {"version": "29.5.3", "resolved": "http://*************:4873/@types/jest/-/jest-29.5.3.tgz", "integrity": "sha512-1Nq7YrO/vJE/FYnqYyw0FS8LdrjExSgIiHyKg7xPpn+yi8Q4huZryKnkJatN1ZRH89Kw2v33/8ZMB7DuZeSLlA==", "dev": true, "dependencies": {"expect": "^29.0.0", "pretty-format": "^29.0.0"}}, "node_modules/@types/lodash": {"version": "4.17.9", "resolved": "http://*************:4873/@types/lodash/-/lodash-4.17.9.tgz", "integrity": "sha512-w9iWudx1XWOHW5lQRS9iKpK/XuRhnN+0T7HvdCCd802FYkT1AMTnxndJHGrNJwRoRHkslGr4S29tjm1cT7x/7w==", "dev": true}, "node_modules/@types/ms": {"version": "0.7.31", "resolved": "http://*************:4873/@types/ms/-/ms-0.7.31.tgz", "integrity": "sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA==", "dev": true}, "node_modules/@types/node": {"version": "20.4.1", "resolved": "http://*************:4873/@types/node/-/node-20.4.1.tgz", "integrity": "sha512-JIzsAvJeA/5iY6Y/OxZbv1lUcc8dNSE77lb2gnBH+/PJ3lFR1Ccvgwl5JWnHAkNHcRsT0TbpVOsiMKZ1F/yyJg==", "dev": true}, "node_modules/@types/prettier": {"version": "2.7.3", "resolved": "http://*************:4873/@types/prettier/-/prettier-2.7.3.tgz", "integrity": "sha512-+68kP9yzs4LMp7VNh8gdzMSPZFL44MLGqiHWvttYJe+6qnuVr4Ek9wSBQoveqY/r+LwjCcU29kNVkidwim+kYA==", "dev": true}, "node_modules/@types/stack-utils": {"version": "2.0.1", "resolved": "http://*************:4873/@types/stack-utils/-/stack-utils-2.0.1.tgz", "integrity": "sha1-IPGClPeX8iCbX2XI47XI6CYdEnw=", "dev": true}, "node_modules/@types/validator": {"version": "13.12.2", "resolved": "http://*************:4873/@types/validator/-/validator-13.12.2.tgz", "integrity": "sha512-6SlHBzUW8Jhf3liqrGGXyTJSIFe4nqlJ5A5KaMZ2l/vbM3Wh3KSybots/wfWVzNLK4D1NZluDlSQIbIEPx6oyA==", "dev": true}, "node_modules/@types/yargs": {"version": "17.0.24", "resolved": "http://*************:4873/@types/yargs/-/yargs-17.0.24.tgz", "integrity": "sha512-6i0aC7jV6QzQB8ne1joVZ0eSFIstHsCrobmOtghM11yGlH0j43FKL2UhWdELkyps0zuf7qVTUVCCR+tgSlyLLw==", "dev": true, "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.0", "resolved": "http://*************:4873/@types/yargs-parser/-/yargs-parser-21.0.0.tgz", "integrity": "sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==", "dev": true}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "http://*************:4873/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=", "dev": true, "optional": true}, "node_modules/agent-base": {"version": "6.0.2", "resolved": "http://*************:4873/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=", "dev": true, "optional": true, "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/agentkeepalive": {"version": "4.3.0", "resolved": "http://*************:4873/agentkeepalive/-/agentkeepalive-4.3.0.tgz", "integrity": "sha512-7Epl1Blf4Sy37j4v9f9FjICCh4+KAQOyXgHEwlyBiAQLbhKdq/i2QQU3amQalS/wPhdPzDXPL5DMR5bkn+YeWg==", "dev": true, "optional": true, "dependencies": {"debug": "^4.1.0", "depd": "^2.0.0", "humanize-ms": "^1.2.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/aggregate-error": {"version": "3.1.0", "resolved": "http://*************:4873/aggregate-error/-/aggregate-error-3.1.0.tgz", "integrity": "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=", "dev": true, "optional": true, "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "resolved": "http://*************:4873/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "integrity": "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=", "dev": true, "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://*************:4873/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "http://*************:4873/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/ansis": {"version": "3.3.2", "resolved": "http://*************:4873/ansis/-/ansis-3.3.2.tgz", "integrity": "sha512-cFthbBlt+Oi0i9Pv/j6YdVWJh54CtjGACaMPCIrEV4Ha7HWsIjXDwseYV79TIL0B4+KfSwD5S70PeQDkPUd1rA==", "dev": true, "engines": {"node": ">=15"}}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "http://*************:4873/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "dev": true, "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/aproba": {"version": "2.0.0", "resolved": "http://*************:4873/aproba/-/aproba-2.0.0.tgz", "integrity": "sha1-UlILiuW1aSFbNU78DKo/4eRaitw=", "dev": true, "optional": true}, "node_modules/argparse": {"version": "1.0.10", "resolved": "http://*************:4873/argparse/-/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "dev": true, "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/babel-jest": {"version": "29.6.1", "resolved": "http://*************:4873/babel-jest/-/babel-jest-29.6.1.tgz", "integrity": "sha512-qu+3bdPEQC6KZSPz+4Fyjbga5OODNcp49j6GKzG1EKbkfyJBxEYGVUmVGpwCSeGouG52R4EgYMLb6p9YeEEQ4A==", "dev": true, "dependencies": {"@jest/transform": "^29.6.1", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.5.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "resolved": "http://*************:4873/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "integrity": "sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-jest-hoist": {"version": "29.5.0", "resolved": "http://*************:4873/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.5.0.tgz", "integrity": "sha512-zSuuuAlTMT4mzLj2nPnUm6fsE6270vdOfnpbJ+RmruU75UhLFvL0N2NgI7xpeS7NaB6hGqmd5pVpGTDYvi4Q3w==", "dev": true, "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/babel-preset-current-node-syntax": {"version": "1.0.1", "resolved": "http://*************:4873/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz", "integrity": "sha1-tDmSObibKgEfndvj5PQB/EDP9zs=", "dev": true, "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-top-level-await": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/babel-preset-jest": {"version": "29.5.0", "resolved": "http://*************:4873/babel-preset-jest/-/babel-preset-jest-29.5.0.tgz", "integrity": "sha512-JOMloxOqdiBSxMAzjRaH023/vvcaSaec49zvg+2LmNsktC7ei39LTJGw02J+9uUtTZUq6xbLyJ4dxe9sSmIuAg==", "dev": true, "dependencies": {"babel-plugin-jest-hoist": "^29.5.0", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "http://*************:4873/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "http://*************:4873/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/bindings": {"version": "1.5.0", "resolved": "http://*************:4873/bindings/-/bindings-1.5.0.tgz", "integrity": "sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==", "dev": true, "dependencies": {"file-uri-to-path": "1.0.0"}}, "node_modules/bl": {"version": "4.1.0", "resolved": "http://*************:4873/bl/-/bl-4.1.0.tgz", "integrity": "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==", "dev": true, "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/bnf-parser": {"version": "3.1.6", "resolved": "http://*************:4873/bnf-parser/-/bnf-parser-3.1.6.tgz", "integrity": "sha512-3x0ECh6CghmcAYnY6uiVAOfl263XkWffDq5fQS20ac3k0U7TE5rTWNXTnOTckgmfZc94iharwqCyoV8OAYxYoA==", "dev": true}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://*************:4873/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "resolved": "http://*************:4873/braces/-/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "dev": true, "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.21.9", "resolved": "http://*************:4873/browserslist/-/browserslist-4.21.9.tgz", "integrity": "sha512-M0MFoZzbUrRU4KNfCrDLnvyE7gub+peetoTid3TBIqtunaDJyXlwhakT+/VkvSXcfIzFfK/nkCs4nmyTmxdNSg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"caniuse-lite": "^1.0.30001503", "electron-to-chromium": "^1.4.431", "node-releases": "^2.0.12", "update-browserslist-db": "^1.0.11"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bs-logger": {"version": "0.2.6", "resolved": "http://*************:4873/bs-logger/-/bs-logger-0.2.6.tgz", "integrity": "sha1-6302UwenLPl0zGzadraDVK0za9g=", "dev": true, "dependencies": {"fast-json-stable-stringify": "2.x"}, "engines": {"node": ">= 6"}}, "node_modules/bser": {"version": "2.1.1", "resolved": "http://*************:4873/bser/-/bser-2.1.1.tgz", "integrity": "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=", "dev": true, "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/buffer": {"version": "5.7.1", "resolved": "http://*************:4873/buffer/-/buffer-5.7.1.tgz", "integrity": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "http://*************:4873/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=", "dev": true}, "node_modules/cacache": {"version": "15.3.0", "resolved": "http://*************:4873/cacache/-/cacache-15.3.0.tgz", "integrity": "sha1-3IU4D7L1Vv492kxxm/oOyHWn8es=", "dev": true, "optional": true, "dependencies": {"@npmcli/fs": "^1.0.0", "@npmcli/move-file": "^1.0.1", "chownr": "^2.0.0", "fs-minipass": "^2.0.0", "glob": "^7.1.4", "infer-owner": "^1.0.4", "lru-cache": "^6.0.0", "minipass": "^3.1.1", "minipass-collect": "^1.0.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.2", "mkdirp": "^1.0.3", "p-map": "^4.0.0", "promise-inflight": "^1.0.1", "rimraf": "^3.0.2", "ssri": "^8.0.1", "tar": "^6.0.2", "unique-filename": "^1.1.1"}, "engines": {"node": ">= 10"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "http://*************:4873/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "5.3.1", "resolved": "http://*************:4873/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001515", "resolved": "http://*************:4873/caniuse-lite/-/caniuse-lite-1.0.30001515.tgz", "integrity": "sha512-eEFDwUOZbE24sb+Ecsx3+OvNETqjWIdabMy52oOkIgcUtAsQifjUG9q4U9dgTHJM2mfk4uEPxc0+xuFdJ629QA==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}]}, "node_modules/chalk": {"version": "4.1.2", "resolved": "http://*************:4873/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/char-regex": {"version": "1.0.2", "resolved": "http://*************:4873/char-regex/-/char-regex-1.0.2.tgz", "integrity": "sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=", "dev": true, "engines": {"node": ">=10"}}, "node_modules/chownr": {"version": "2.0.0", "resolved": "http://*************:4873/chownr/-/chownr-2.0.0.tgz", "integrity": "sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=", "dev": true, "engines": {"node": ">=10"}}, "node_modules/ci-info": {"version": "3.8.0", "resolved": "http://*************:4873/ci-info/-/ci-info-3.8.0.tgz", "integrity": "sha512-eXTggHWSooYhq49F2opQhuHWgzucfF2YgODK4e1566GQs5BIfP30B0oenwBJHfWxAs2fyPB1s7Mg949zLf61Yw==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "engines": {"node": ">=8"}}, "node_modules/cjs-module-lexer": {"version": "1.2.3", "resolved": "http://*************:4873/cjs-module-lexer/-/cjs-module-lexer-1.2.3.tgz", "integrity": "sha512-0TNiGstbQmCFwt4akjjBg5pLRTSyj/PkWQ1ZoO2zntmg9yLqSRxwEa4iCfQLGjqhiqBfOJa7W/E8wfGrTDmlZQ==", "dev": true}, "node_modules/clean-stack": {"version": "2.2.0", "resolved": "http://*************:4873/clean-stack/-/clean-stack-2.2.0.tgz", "integrity": "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=", "dev": true, "optional": true, "engines": {"node": ">=6"}}, "node_modules/cliui": {"version": "8.0.1", "resolved": "http://*************:4873/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "dev": true, "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/co": {"version": "4.6.0", "resolved": "http://*************:4873/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "dev": true, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/collect-v8-coverage": {"version": "1.0.2", "resolved": "http://*************:4873/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "integrity": "sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==", "dev": true}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "http://*************:4873/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "http://*************:4873/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "node_modules/color-support": {"version": "1.1.3", "resolved": "http://*************:4873/color-support/-/color-support-1.1.3.tgz", "integrity": "sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI=", "dev": true, "optional": true, "bin": {"color-support": "bin.js"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://*************:4873/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "node_modules/console-control-strings": {"version": "1.1.0", "resolved": "http://*************:4873/console-control-strings/-/console-control-strings-1.1.0.tgz", "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=", "dev": true, "optional": true}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "http://*************:4873/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true}, "node_modules/cookie": {"version": "0.5.0", "resolved": "http://*************:4873/cookie/-/cookie-0.5.0.tgz", "integrity": "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==", "engines": {"node": ">= 0.6"}}, "node_modules/cross-spawn": {"version": "7.0.3", "resolved": "http://*************:4873/cross-spawn/-/cross-spawn-7.0.3.tgz", "integrity": "sha1-9zqFudXUHQRVUcF34ogtSshXKKY=", "dev": true, "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "http://*************:4873/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==", "dev": true}, "node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decompress-response": {"version": "6.0.0", "resolved": "http://*************:4873/decompress-response/-/decompress-response-6.0.0.tgz", "integrity": "sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==", "dev": true, "dependencies": {"mimic-response": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/dedent": {"version": "0.7.0", "resolved": "http://*************:4873/dedent/-/dedent-0.7.0.tgz", "integrity": "sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=", "dev": true}, "node_modules/deep-extend": {"version": "0.6.0", "resolved": "http://*************:4873/deep-extend/-/deep-extend-0.6.0.tgz", "integrity": "sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=", "dev": true, "engines": {"node": ">=4.0.0"}}, "node_modules/deepmerge": {"version": "4.3.1", "resolved": "http://*************:4873/deepmerge/-/deepmerge-4.3.1.tgz", "integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/delegates": {"version": "1.0.0", "resolved": "http://*************:4873/delegates/-/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=", "dev": true, "optional": true}, "node_modules/depd": {"version": "2.0.0", "resolved": "http://*************:4873/depd/-/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "dev": true, "optional": true, "engines": {"node": ">= 0.8"}}, "node_modules/detect-libc": {"version": "2.0.3", "resolved": "http://*************:4873/detect-libc/-/detect-libc-2.0.3.tgz", "integrity": "sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/detect-newline": {"version": "3.1.0", "resolved": "http://*************:4873/detect-newline/-/detect-newline-3.1.0.tgz", "integrity": "sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/diff-sequences": {"version": "29.4.3", "resolved": "http://*************:4873/diff-sequences/-/diff-sequences-29.4.3.tgz", "integrity": "sha512-ofrBgwpPhCD85kMKtE9RYFFq6OC1A89oW2vvgWZNCwxrUpRUILopY7lsYyMDSjc8g6U6aiO0Qubg6r4Wgt5ZnA==", "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/dottie": {"version": "2.0.6", "resolved": "http://*************:4873/dottie/-/dottie-2.0.6.tgz", "integrity": "sha512-iGCHkfUc5kFekGiqhe8B/mdaurD+lakO9txNnTvKtA6PISrw86LgqHvRzWYPyoE2Ph5aMIrCw9/uko6XHTKCwA==", "dev": true}, "node_modules/electron-to-chromium": {"version": "1.4.457", "resolved": "http://*************:4873/electron-to-chromium/-/electron-to-chromium-1.4.457.tgz", "integrity": "sha512-/g3UyNDmDd6ebeWapmAoiyy+Sy2HyJ+/X8KyvNeHfKRFfHaA2W8oF5fxD5F3tjBDcjpwo0iek6YNgxNXDBoEtA==", "dev": true}, "node_modules/emittery": {"version": "0.13.1", "resolved": "http://*************:4873/emittery/-/emittery-0.13.1.tgz", "integrity": "sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==", "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://*************:4873/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true}, "node_modules/encoding": {"version": "0.1.13", "resolved": "http://*************:4873/encoding/-/encoding-0.1.13.tgz", "integrity": "sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=", "dev": true, "optional": true, "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "http://*************:4873/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "dev": true, "dependencies": {"once": "^1.4.0"}}, "node_modules/env-paths": {"version": "2.2.1", "resolved": "http://*************:4873/env-paths/-/env-paths-2.2.1.tgz", "integrity": "sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=", "dev": true, "optional": true, "engines": {"node": ">=6"}}, "node_modules/err-code": {"version": "2.0.3", "resolved": "http://*************:4873/err-code/-/err-code-2.0.3.tgz", "integrity": "sha1-I8Lzt1b/38YI0w4nyalBAkgH5/k=", "dev": true, "optional": true}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "http://*************:4873/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/escalade": {"version": "3.1.1", "resolved": "http://*************:4873/escalade/-/escalade-3.1.1.tgz", "integrity": "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "2.0.0", "resolved": "http://*************:4873/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "integrity": "sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "http://*************:4873/esprima/-/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "dev": true, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/execa": {"version": "5.1.1", "resolved": "http://*************:4873/execa/-/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "dev": true, "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/exit": {"version": "0.1.2", "resolved": "http://*************:4873/exit/-/exit-0.1.2.tgz", "integrity": "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/expand-template": {"version": "2.0.3", "resolved": "http://*************:4873/expand-template/-/expand-template-2.0.3.tgz", "integrity": "sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/expect": {"version": "29.6.1", "resolved": "http://*************:4873/expect/-/expect-29.6.1.tgz", "integrity": "sha512-XEdDLonERCU1n9uR56/Stx9OqojaLAQtZf9PrCHH9Hl8YXiEIka3H4NXJ3NOIBmQJTg7+j7buh34PMHfJujc8g==", "dev": true, "dependencies": {"@jest/expect-utils": "^29.6.1", "@types/node": "*", "jest-get-type": "^29.4.3", "jest-matcher-utils": "^29.6.1", "jest-message-util": "^29.6.1", "jest-util": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/fast-glob": {"version": "3.3.2", "resolved": "http://*************:4873/fast-glob/-/fast-glob-3.3.2.tgz", "integrity": "sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==", "dev": true, "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://*************:4873/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true}, "node_modules/fastq": {"version": "1.17.1", "resolved": "http://*************:4873/fastq/-/fastq-1.17.1.tgz", "integrity": "sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==", "dev": true, "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fb-watchman": {"version": "2.0.2", "resolved": "http://*************:4873/fb-watchman/-/fb-watchman-2.0.2.tgz", "integrity": "sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==", "dev": true, "dependencies": {"bser": "2.1.1"}}, "node_modules/file-uri-to-path": {"version": "1.0.0", "resolved": "http://*************:4873/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "integrity": "sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==", "dev": true}, "node_modules/fill-range": {"version": "7.0.1", "resolved": "http://*************:4873/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "dev": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "4.1.0", "resolved": "http://*************:4873/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs-constants": {"version": "1.0.0", "resolved": "http://*************:4873/fs-constants/-/fs-constants-1.0.0.tgz", "integrity": "sha1-a+Dem+mYzhavivwkSXue6bfM2a0=", "dev": true}, "node_modules/fs-minipass": {"version": "2.1.0", "resolved": "http://*************:4873/fs-minipass/-/fs-minipass-2.1.0.tgz", "integrity": "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=", "dev": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "http://*************:4873/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "http://*************:4873/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "http://*************:4873/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "http://*************:4873/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true, "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-package-type": {"version": "0.1.0", "resolved": "http://*************:4873/get-package-type/-/get-package-type-0.1.0.tgz", "integrity": "sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=", "dev": true, "engines": {"node": ">=8.0.0"}}, "node_modules/get-stream": {"version": "6.0.1", "resolved": "http://*************:4873/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/github-from-package": {"version": "0.0.0", "resolved": "http://*************:4873/github-from-package/-/github-from-package-0.0.0.tgz", "integrity": "sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4=", "dev": true}, "node_modules/glob": {"version": "7.2.3", "resolved": "http://*************:4873/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "http://*************:4873/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/globals": {"version": "11.12.0", "resolved": "http://*************:4873/globals/-/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "http://*************:4873/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true}, "node_modules/has": {"version": "1.0.3", "resolved": "http://*************:4873/has/-/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "dev": true, "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "http://*************:4873/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/has-unicode": {"version": "2.0.1", "resolved": "http://*************:4873/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=", "dev": true, "optional": true}, "node_modules/html-escaper": {"version": "2.0.2", "resolved": "http://*************:4873/html-escaper/-/html-escaper-2.0.2.tgz", "integrity": "sha1-39YAJ9o2o238viNiYsAKWCJoFFM=", "dev": true}, "node_modules/http-cache-semantics": {"version": "4.1.1", "resolved": "http://*************:4873/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz", "integrity": "sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==", "dev": true, "optional": true}, "node_modules/http-proxy-agent": {"version": "4.0.1", "resolved": "http://*************:4873/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "integrity": "sha1-ioyO9/WTLM+VPClsqCkblap0qjo=", "dev": true, "optional": true, "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "resolved": "http://*************:4873/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "integrity": "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==", "dev": true, "optional": true, "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/human-signals": {"version": "2.1.0", "resolved": "http://*************:4873/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=", "dev": true, "engines": {"node": ">=10.17.0"}}, "node_modules/humanize-ms": {"version": "1.2.1", "resolved": "http://*************:4873/humanize-ms/-/humanize-ms-1.2.1.tgz", "integrity": "sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=", "dev": true, "optional": true, "dependencies": {"ms": "^2.0.0"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "http://*************:4873/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "dev": true, "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "http://*************:4873/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/import-local": {"version": "3.1.0", "resolved": "http://*************:4873/import-local/-/import-local-3.1.0.tgz", "integrity": "sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==", "dev": true, "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "http://*************:4873/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "resolved": "http://*************:4873/indent-string/-/indent-string-4.0.0.tgz", "integrity": "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=", "dev": true, "optional": true, "engines": {"node": ">=8"}}, "node_modules/infer-owner": {"version": "1.0.4", "resolved": "http://*************:4873/infer-owner/-/infer-owner-1.0.4.tgz", "integrity": "sha1-xM78qo5RBRwqQLos6KPScpWvlGc=", "dev": true, "optional": true}, "node_modules/inflection": {"version": "1.13.4", "resolved": "https://registry.npmmirror.com/inflection/-/inflection-1.13.4.tgz", "integrity": "sha512-6I/HUDeYFfuNCVS3td055BaXBwKYuzw7K3ExVMStBowKo9oOAMJIXIHvdyR3iboTCp1b+1i5DSkIZTcwIktuDw==", "dev": true, "engines": ["node >= 0.4.0"]}, "node_modules/inflight": {"version": "1.0.6", "resolved": "http://*************:4873/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "http://*************:4873/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "dev": true}, "node_modules/ini": {"version": "1.3.8", "resolved": "http://*************:4873/ini/-/ini-1.3.8.tgz", "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==", "dev": true}, "node_modules/ip": {"version": "2.0.0", "resolved": "http://*************:4873/ip/-/ip-2.0.0.tgz", "integrity": "sha512-WKa+XuLG1A1R0UWhl2+1XQSi+fZWMsYKffMZTTYsiZaUD8k2yDAj5atimTUD2TZkyCkNEeYE5NhFZmupOGtjYQ==", "dev": true, "optional": true}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "http://*************:4873/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "node_modules/is-core-module": {"version": "2.12.1", "resolved": "http://*************:4873/is-core-module/-/is-core-module-2.12.1.tgz", "integrity": "sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg==", "dev": true, "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://*************:4873/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://*************:4873/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/is-generator-fn": {"version": "2.1.0", "resolved": "http://*************:4873/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "integrity": "sha1-fRQK3DiarzARqPKipM+m+q3/sRg=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://*************:4873/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-json": {"version": "2.0.1", "resolved": "http://*************:4873/is-json/-/is-json-2.0.1.tgz", "integrity": "sha1-a+Fm0USCihMdaGiRuYPfYsOUkf8="}, "node_modules/is-lambda": {"version": "1.0.1", "resolved": "http://*************:4873/is-lambda/-/is-lambda-1.0.1.tgz", "integrity": "sha1-PZh3iZ5qU+/AFgUEzeFfgubwYdU=", "dev": true, "optional": true}, "node_modules/is-number": {"version": "7.0.0", "resolved": "http://*************:4873/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "engines": {"node": ">=0.12.0"}}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "http://*************:4873/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://*************:4873/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "node_modules/istanbul-lib-coverage": {"version": "3.2.0", "resolved": "http://*************:4873/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz", "integrity": "sha1-GJ55CdCjn6Wj361bA/cZR3cBkdM=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "5.2.1", "resolved": "http://*************:4873/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "integrity": "sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==", "dev": true, "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument/node_modules/semver": {"version": "6.3.1", "resolved": "http://*************:4873/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/istanbul-lib-report": {"version": "3.0.0", "resolved": "http://*************:4873/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "integrity": "sha1-dRj+UupE3jcvRgp2tezan/tz2KY=", "dev": true, "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^3.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "resolved": "http://*************:4873/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "integrity": "sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=", "dev": true, "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-reports": {"version": "3.1.5", "resolved": "http://*************:4873/istanbul-reports/-/istanbul-reports-3.1.5.tgz", "integrity": "sha512-nUsEMa9pBt/NOHqbcbeJEgqIlY/K7rVWUX6Lql2orY5e9roQOthbR3vtY4zzf2orPELg80fnxxk9zUyPlgwD1w==", "dev": true, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest": {"version": "29.6.1", "resolved": "http://*************:4873/jest/-/jest-29.6.1.tgz", "integrity": "sha512-Nirw5B4nn69rVUZtemCQhwxOBhm0nsp3hmtF4rzCeWD7BkjAXRIji7xWQfnTNbz9g0aVsBX6aZK3n+23LM6uDw==", "dev": true, "dependencies": {"@jest/core": "^29.6.1", "@jest/types": "^29.6.1", "import-local": "^3.0.2", "jest-cli": "^29.6.1"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-changed-files": {"version": "29.5.0", "resolved": "http://*************:4873/jest-changed-files/-/jest-changed-files-29.5.0.tgz", "integrity": "sha512-IFG34IUMUaNBIxjQXF/iu7g6EcdMrGRRxaUSw92I/2g2YC6vCdTltl4nHvt7Ci5nSJwXIkCu8Ka1DKF+X7Z1Ag==", "dev": true, "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-circus": {"version": "29.6.1", "resolved": "http://*************:4873/jest-circus/-/jest-circus-29.6.1.tgz", "integrity": "sha512-tPbYLEiBU4MYAL2XoZme/bgfUeotpDBd81lgHLCbDZZFaGmECk0b+/xejPFtmiBP87GgP/y4jplcRpbH+fgCzQ==", "dev": true, "dependencies": {"@jest/environment": "^29.6.1", "@jest/expect": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^0.7.0", "is-generator-fn": "^2.0.0", "jest-each": "^29.6.1", "jest-matcher-utils": "^29.6.1", "jest-message-util": "^29.6.1", "jest-runtime": "^29.6.1", "jest-snapshot": "^29.6.1", "jest-util": "^29.6.1", "p-limit": "^3.1.0", "pretty-format": "^29.6.1", "pure-rand": "^6.0.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-cli": {"version": "29.6.1", "resolved": "http://*************:4873/jest-cli/-/jest-cli-29.6.1.tgz", "integrity": "sha512-607dSgTA4ODIN6go9w6xY3EYkyPFGicx51a69H7yfvt7lN53xNswEVLovq+E77VsTRi5fWprLH0yl4DJgE8Ing==", "dev": true, "dependencies": {"@jest/core": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/types": "^29.6.1", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "import-local": "^3.0.2", "jest-config": "^29.6.1", "jest-util": "^29.6.1", "jest-validate": "^29.6.1", "prompts": "^2.0.1", "yargs": "^17.3.1"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-config": {"version": "29.6.1", "resolved": "http://*************:4873/jest-config/-/jest-config-29.6.1.tgz", "integrity": "sha512-XdjYV2fy2xYixUiV2Wc54t3Z4oxYPAELUzWnV6+mcbq0rh742X2p52pii5A3oeRzYjLnQxCsZmp0qpI6klE2cQ==", "dev": true, "dependencies": {"@babel/core": "^7.11.6", "@jest/test-sequencer": "^29.6.1", "@jest/types": "^29.6.1", "babel-jest": "^29.6.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-circus": "^29.6.1", "jest-environment-node": "^29.6.1", "jest-get-type": "^29.4.3", "jest-regex-util": "^29.4.3", "jest-resolve": "^29.6.1", "jest-runner": "^29.6.1", "jest-util": "^29.6.1", "jest-validate": "^29.6.1", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^29.6.1", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@types/node": "*", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/jest-diff": {"version": "29.6.1", "resolved": "http://*************:4873/jest-diff/-/jest-diff-29.6.1.tgz", "integrity": "sha512-FsNCvinvl8oVxpNLttNQX7FAq7vR+gMDGj90tiP7siWw1UdakWUGqrylpsYrpvj908IYckm5Y0Q7azNAozU1Kg==", "dev": true, "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.4.3", "jest-get-type": "^29.4.3", "pretty-format": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-docblock": {"version": "29.4.3", "resolved": "http://*************:4873/jest-docblock/-/jest-docblock-29.4.3.tgz", "integrity": "sha512-fzdTftThczeSD9nZ3fzA/4KkHtnmllawWrXO69vtI+L9WjEIuXWs4AmyME7lN5hU7dB0sHhuPfcKofRsUb/2Fg==", "dev": true, "dependencies": {"detect-newline": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-each": {"version": "29.6.1", "resolved": "http://*************:4873/jest-each/-/jest-each-29.6.1.tgz", "integrity": "sha512-n5eoj5eiTHpKQCAVcNTT7DRqeUmJ01hsAL0Q1SMiBHcBcvTKDELixQOGMCpqhbIuTcfC4kMfSnpmDqRgRJcLNQ==", "dev": true, "dependencies": {"@jest/types": "^29.6.1", "chalk": "^4.0.0", "jest-get-type": "^29.4.3", "jest-util": "^29.6.1", "pretty-format": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-environment-node": {"version": "29.6.1", "resolved": "http://*************:4873/jest-environment-node/-/jest-environment-node-29.6.1.tgz", "integrity": "sha512-ZNIfAiE+foBog24W+2caIldl4Irh8Lx1PUhg/GZ0odM1d/h2qORAsejiFc7zb+SEmYPn1yDZzEDSU5PmDkmVLQ==", "dev": true, "dependencies": {"@jest/environment": "^29.6.1", "@jest/fake-timers": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "jest-mock": "^29.6.1", "jest-util": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-get-type": {"version": "29.4.3", "resolved": "http://*************:4873/jest-get-type/-/jest-get-type-29.4.3.tgz", "integrity": "sha512-J5Xez4nRRMjk8emnTpWrlkyb9pfRQQanDrvWHhsR1+VUfbwxi30eVcZFlcdGInRibU4G5LwHXpI7IRHU0CY+gg==", "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-haste-map": {"version": "29.6.1", "resolved": "http://*************:4873/jest-haste-map/-/jest-haste-map-29.6.1.tgz", "integrity": "sha512-0m7f9PZXxOCk1gRACiVgX85knUKPKLPg4oRCjLoqIm9brTHXaorMA0JpmtmVkQiT8nmXyIVoZd/nnH1cfC33ig==", "dev": true, "dependencies": {"@jest/types": "^29.6.1", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.4.3", "jest-util": "^29.6.1", "jest-worker": "^29.6.1", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/jest-leak-detector": {"version": "29.6.1", "resolved": "http://*************:4873/jest-leak-detector/-/jest-leak-detector-29.6.1.tgz", "integrity": "sha512-OrxMNyZirpOEwkF3UHnIkAiZbtkBWiye+hhBweCHkVbCgyEy71Mwbb5zgeTNYWJBi1qgDVfPC1IwO9dVEeTLwQ==", "dev": true, "dependencies": {"jest-get-type": "^29.4.3", "pretty-format": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-matcher-utils": {"version": "29.6.1", "resolved": "http://*************:4873/jest-matcher-utils/-/jest-matcher-utils-29.6.1.tgz", "integrity": "sha512-SLaztw9d2mfQQKHmJXKM0HCbl2PPVld/t9Xa6P9sgiExijviSp7TnZZpw2Fpt+OI3nwUO/slJbOfzfUMKKC5QA==", "dev": true, "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.6.1", "jest-get-type": "^29.4.3", "pretty-format": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-message-util": {"version": "29.6.1", "resolved": "http://*************:4873/jest-message-util/-/jest-message-util-29.6.1.tgz", "integrity": "sha512-KoAW2zAmNSd3Gk88uJ56qXUWbFk787QKmjjJVOjtGFmmGSZgDBrlIL4AfQw1xyMYPNVD7dNInfIbur9B2rd/wQ==", "dev": true, "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.1", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.6.1", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-mock": {"version": "29.6.1", "resolved": "http://*************:4873/jest-mock/-/jest-mock-29.6.1.tgz", "integrity": "sha512-brovyV9HBkjXAEdRooaTQK42n8usKoSRR3gihzUpYeV/vwqgSoNfrksO7UfSACnPmxasO/8TmHM3w9Hp3G1dgw==", "dev": true, "dependencies": {"@jest/types": "^29.6.1", "@types/node": "*", "jest-util": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-pnp-resolver": {"version": "1.2.3", "resolved": "http://*************:4873/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "integrity": "sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==", "dev": true, "engines": {"node": ">=6"}, "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}}, "node_modules/jest-regex-util": {"version": "29.4.3", "resolved": "http://*************:4873/jest-regex-util/-/jest-regex-util-29.4.3.tgz", "integrity": "sha512-O4FglZaMmWXbGHSQInfXewIsd1LMn9p3ZXB/6r4FOkyhX2/iP/soMG98jGvk/A3HAN78+5VWcBGO0BJAPRh4kg==", "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve": {"version": "29.6.1", "resolved": "http://*************:4873/jest-resolve/-/jest-resolve-29.6.1.tgz", "integrity": "sha512-AeRkyS8g37UyJiP9w3mmI/VXU/q8l/IH52vj/cDAyScDcemRbSBhfX/NMYIGilQgSVwsjxrCHf3XJu4f+lxCMg==", "dev": true, "dependencies": {"chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "jest-pnp-resolver": "^1.2.2", "jest-util": "^29.6.1", "jest-validate": "^29.6.1", "resolve": "^1.20.0", "resolve.exports": "^2.0.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve-dependencies": {"version": "29.6.1", "resolved": "http://*************:4873/jest-resolve-dependencies/-/jest-resolve-dependencies-29.6.1.tgz", "integrity": "sha512-BbFvxLXtcldaFOhNMXmHRWx1nXQO5LoXiKSGQcA1LxxirYceZT6ch8KTE1bK3X31TNG/JbkI7OkS/ABexVahiw==", "dev": true, "dependencies": {"jest-regex-util": "^29.4.3", "jest-snapshot": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runner": {"version": "29.6.1", "resolved": "http://*************:4873/jest-runner/-/jest-runner-29.6.1.tgz", "integrity": "sha512-tw0wb2Q9yhjAQ2w8rHRDxteryyIck7gIzQE4Reu3JuOBpGp96xWgF0nY8MDdejzrLCZKDcp8JlZrBN/EtkQvPQ==", "dev": true, "dependencies": {"@jest/console": "^29.6.1", "@jest/environment": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.13.1", "graceful-fs": "^4.2.9", "jest-docblock": "^29.4.3", "jest-environment-node": "^29.6.1", "jest-haste-map": "^29.6.1", "jest-leak-detector": "^29.6.1", "jest-message-util": "^29.6.1", "jest-resolve": "^29.6.1", "jest-runtime": "^29.6.1", "jest-util": "^29.6.1", "jest-watcher": "^29.6.1", "jest-worker": "^29.6.1", "p-limit": "^3.1.0", "source-map-support": "0.5.13"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runtime": {"version": "29.6.1", "resolved": "http://*************:4873/jest-runtime/-/jest-runtime-29.6.1.tgz", "integrity": "sha512-D6/AYOA+Lhs5e5il8+5pSLemjtJezUr+8zx+Sn8xlmOux3XOqx4d8l/2udBea8CRPqqrzhsKUsN/gBDE/IcaPQ==", "dev": true, "dependencies": {"@jest/environment": "^29.6.1", "@jest/fake-timers": "^29.6.1", "@jest/globals": "^29.6.1", "@jest/source-map": "^29.6.0", "@jest/test-result": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "cjs-module-lexer": "^1.0.0", "collect-v8-coverage": "^1.0.0", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "jest-message-util": "^29.6.1", "jest-mock": "^29.6.1", "jest-regex-util": "^29.4.3", "jest-resolve": "^29.6.1", "jest-snapshot": "^29.6.1", "jest-util": "^29.6.1", "slash": "^3.0.0", "strip-bom": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-snapshot": {"version": "29.6.1", "resolved": "http://*************:4873/jest-snapshot/-/jest-snapshot-29.6.1.tgz", "integrity": "sha512-G4UQE1QQ6OaCgfY+A0uR1W2AY0tGXUPQpoUClhWHq1Xdnx1H6JOrC2nH5lqnOEqaDgbHFgIwZ7bNq24HpB180A==", "dev": true, "dependencies": {"@babel/core": "^7.11.6", "@babel/generator": "^7.7.2", "@babel/plugin-syntax-jsx": "^7.7.2", "@babel/plugin-syntax-typescript": "^7.7.2", "@babel/types": "^7.3.3", "@jest/expect-utils": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@types/prettier": "^2.1.5", "babel-preset-current-node-syntax": "^1.0.0", "chalk": "^4.0.0", "expect": "^29.6.1", "graceful-fs": "^4.2.9", "jest-diff": "^29.6.1", "jest-get-type": "^29.4.3", "jest-matcher-utils": "^29.6.1", "jest-message-util": "^29.6.1", "jest-util": "^29.6.1", "natural-compare": "^1.4.0", "pretty-format": "^29.6.1", "semver": "^7.5.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-util": {"version": "29.6.1", "resolved": "http://*************:4873/jest-util/-/jest-util-29.6.1.tgz", "integrity": "sha512-NRFCcjc+/uO3ijUVyNOQJluf8PtGCe/W6cix36+M3cTFgiYqFOOW5MgN4JOOcvbUhcKTYVd1CvHz/LWi8d16Mg==", "dev": true, "dependencies": {"@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate": {"version": "29.6.1", "resolved": "http://*************:4873/jest-validate/-/jest-validate-29.6.1.tgz", "integrity": "sha512-r3Ds69/0KCN4vx4sYAbGL1EVpZ7MSS0vLmd3gV78O+NAx3PDQQukRU5hNHPXlyqCgFY8XUk7EuTMLugh0KzahA==", "dev": true, "dependencies": {"@jest/types": "^29.6.1", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.4.3", "leven": "^3.1.0", "pretty-format": "^29.6.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate/node_modules/camelcase": {"version": "6.3.0", "resolved": "http://*************:4873/camelcase/-/camelcase-6.3.0.tgz", "integrity": "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watcher": {"version": "29.6.1", "resolved": "http://*************:4873/jest-watcher/-/jest-watcher-29.6.1.tgz", "integrity": "sha512-d4wpjWTS7HEZPaaj8m36QiaP856JthRZkrgcIY/7ISoUWPIillrXM23WPboZVLbiwZBt4/qn2Jke84Sla6JhFA==", "dev": true, "dependencies": {"@jest/test-result": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.6.1", "string-length": "^4.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker": {"version": "29.6.1", "resolved": "http://*************:4873/jest-worker/-/jest-worker-29.6.1.tgz", "integrity": "sha512-U+Wrbca7S8ZAxAe9L6nb6g8kPdia5hj32Puu5iOqBCMTMWFHXuK6dOV2IFrpedbTV8fjMFLdWNttQTBL6u2MRA==", "dev": true, "dependencies": {"@types/node": "*", "jest-util": "^29.6.1", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "resolved": "http://*************:4873/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "dev": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "http://*************:4873/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true}, "node_modules/js-yaml": {"version": "3.14.1", "resolved": "http://*************:4873/js-yaml/-/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "dev": true, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "2.5.2", "resolved": "http://*************:4873/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=", "dev": true, "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "http://*************:4873/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "dev": true}, "node_modules/json5": {"version": "2.2.3", "resolved": "http://*************:4873/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/kleur": {"version": "3.0.3", "resolved": "http://*************:4873/kleur/-/kleur-3.0.3.tgz", "integrity": "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/leven": {"version": "3.1.0", "resolved": "http://*************:4873/leven/-/leven-3.1.0.tgz", "integrity": "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "http://*************:4873/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI=", "dev": true}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "http://*************:4873/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "http://*************:4873/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "node_modules/lodash.memoize": {"version": "4.1.2", "resolved": "http://*************:4873/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "integrity": "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=", "dev": true}, "node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "dev": true, "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/lz-string": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/lz-string/-/lz-string-1.5.0.tgz", "integrity": "sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==", "bin": {"lz-string": "bin/bin.js"}}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "http://*************:4873/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dev": true, "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "resolved": "http://*************:4873/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/make-error": {"version": "1.3.6", "resolved": "http://*************:4873/make-error/-/make-error-1.3.6.tgz", "integrity": "sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=", "dev": true}, "node_modules/make-fetch-happen": {"version": "9.1.0", "resolved": "http://*************:4873/make-fetch-happen/-/make-fetch-happen-9.1.0.tgz", "integrity": "sha1-UwhaCeeXFDPmdl95cb9j9OBcuWg=", "dev": true, "optional": true, "dependencies": {"agentkeepalive": "^4.1.3", "cacache": "^15.2.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "minipass": "^3.1.3", "minipass-collect": "^1.0.2", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "socks-proxy-agent": "^6.0.0", "ssri": "^8.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/makeerror": {"version": "1.0.12", "resolved": "http://*************:4873/makeerror/-/makeerror-1.0.12.tgz", "integrity": "sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==", "dev": true, "dependencies": {"tmpl": "1.0.5"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "http://*************:4873/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=", "dev": true}, "node_modules/merge2": {"version": "1.4.1", "resolved": "http://*************:4873/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.5", "resolved": "http://*************:4873/micromatch/-/micromatch-4.0.5.tgz", "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "dev": true, "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "http://*************:4873/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/mimic-response": {"version": "3.1.0", "resolved": "http://*************:4873/mimic-response/-/mimic-response-3.1.0.tgz", "integrity": "sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "http://*************:4873/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "http://*************:4873/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "3.3.6", "resolved": "http://*************:4873/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "dev": true, "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-collect": {"version": "1.0.2", "resolved": "http://*************:4873/minipass-collect/-/minipass-collect-1.0.2.tgz", "integrity": "sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc=", "dev": true, "optional": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-fetch": {"version": "1.4.1", "resolved": "http://*************:4873/minipass-fetch/-/minipass-fetch-1.4.1.tgz", "integrity": "sha1-114AkdqsGw/9fp1BYp+v99DB8bY=", "dev": true, "optional": true, "dependencies": {"minipass": "^3.1.0", "minipass-sized": "^1.0.3", "minizlib": "^2.0.0"}, "engines": {"node": ">=8"}, "optionalDependencies": {"encoding": "^0.1.12"}}, "node_modules/minipass-flush": {"version": "1.0.5", "resolved": "http://*************:4873/minipass-flush/-/minipass-flush-1.0.5.tgz", "integrity": "sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M=", "dev": true, "optional": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-pipeline": {"version": "1.2.4", "resolved": "http://*************:4873/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz", "integrity": "sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw=", "dev": true, "optional": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-sized": {"version": "1.0.3", "resolved": "http://*************:4873/minipass-sized/-/minipass-sized-1.0.3.tgz", "integrity": "sha1-cO5afFBSBwr6z7wil36nne81O3A=", "dev": true, "optional": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "resolved": "http://*************:4873/minizlib/-/minizlib-2.1.2.tgz", "integrity": "sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=", "dev": true, "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "http://*************:4873/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha1-PrXtYmInVteaXw4qIh3+utdcL34=", "dev": true, "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/mkdirp-classic": {"version": "0.5.3", "resolved": "http://*************:4873/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz", "integrity": "sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==", "dev": true}, "node_modules/moment": {"version": "2.29.4", "resolved": "https://registry.npmmirror.com/moment/-/moment-2.29.4.tgz", "integrity": "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==", "dev": true, "engines": {"node": "*"}}, "node_modules/moment-timezone": {"version": "0.5.43", "resolved": "https://registry.npmmirror.com/moment-timezone/-/moment-timezone-0.5.43.tgz", "integrity": "sha512-72j3aNyuIsDxdF1i7CEgV2FfxM1r6aaqJyLB2vwb33mXYyoyLly+F1zbWqhA3/bVIoJ4szlUoMbUnVdid32NUQ==", "dev": true, "dependencies": {"moment": "^2.29.4"}, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true}, "node_modules/nanoid": {"version": "3.3.7", "resolved": "http://*************:4873/nanoid/-/nanoid-3.3.7.tgz", "integrity": "sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/napi-build-utils": {"version": "1.0.2", "resolved": "http://*************:4873/napi-build-utils/-/napi-build-utils-1.0.2.tgz", "integrity": "sha512-ONmRUqK7zj7DWX0D9ADe03wbwOBZxNAfF20PlGfCWQcD3+/MakShIHrMqx9YwPTfxDdF1zLeL+RGZiR9kGMLdg==", "dev": true}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "http://*************:4873/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "node_modules/negotiator": {"version": "0.6.3", "resolved": "http://*************:4873/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==", "dev": true, "optional": true, "engines": {"node": ">= 0.6"}}, "node_modules/node-abi": {"version": "3.68.0", "resolved": "http://*************:4873/node-abi/-/node-abi-3.68.0.tgz", "integrity": "sha512-7vbj10trelExNjFSBm5kTvZXXa7pZyKWx9RCKIyqe6I9Ev3IzGpQoqBP3a+cOdxY+pWj6VkP28n/2wWysBHD/A==", "dev": true, "dependencies": {"semver": "^7.3.5"}, "engines": {"node": ">=10"}}, "node_modules/node-addon-api": {"version": "7.1.1", "resolved": "http://*************:4873/node-addon-api/-/node-addon-api-7.1.1.tgz", "integrity": "sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==", "dev": true}, "node_modules/node-gyp": {"version": "8.4.1", "resolved": "http://*************:4873/node-gyp/-/node-gyp-8.4.1.tgz", "integrity": "sha1-PUkwj8MfdoGAlX1rV0aEX71CmTc=", "dev": true, "optional": true, "dependencies": {"env-paths": "^2.2.0", "glob": "^7.1.4", "graceful-fs": "^4.2.6", "make-fetch-happen": "^9.1.0", "nopt": "^5.0.0", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.2", "which": "^2.0.2"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": ">= 10.12.0"}}, "node_modules/node-gyp/node_modules/are-we-there-yet": {"version": "3.0.1", "resolved": "http://*************:4873/are-we-there-yet/-/are-we-there-yet-3.0.1.tgz", "integrity": "sha512-QZW4EDmGwlYur0Yyf/b2uGucHQMa8aFUP7eu9ddR73vvhFyt4V0Vl3QHPcTNJ8l6qYOBdxgXdnBXQrHilfRQBg==", "dev": true, "optional": true, "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/node-gyp/node_modules/gauge": {"version": "4.0.4", "resolved": "http://*************:4873/gauge/-/gauge-4.0.4.tgz", "integrity": "sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg==", "dev": true, "optional": true, "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.3", "console-control-strings": "^1.1.0", "has-unicode": "^2.0.1", "signal-exit": "^3.0.7", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/node-gyp/node_modules/npmlog": {"version": "6.0.2", "resolved": "http://*************:4873/npmlog/-/npmlog-6.0.2.tgz", "integrity": "sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==", "dev": true, "optional": true, "dependencies": {"are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0", "gauge": "^4.0.3", "set-blocking": "^2.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/node-int64": {"version": "0.4.0", "resolved": "http://*************:4873/node-int64/-/node-int64-0.4.0.tgz", "integrity": "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=", "dev": true}, "node_modules/node-releases": {"version": "2.0.13", "resolved": "http://*************:4873/node-releases/-/node-releases-2.0.13.tgz", "integrity": "sha512-uYr7J37ae/ORWdZeQ1xxMJe3NtdmqMC/JZK+geofDrkLUApKRHPd18/TxtBOJ4A0/+uUIliorNrfYV6s1b02eQ==", "dev": true}, "node_modules/nopt": {"version": "5.0.0", "resolved": "http://*************:4873/nopt/-/nopt-5.0.0.tgz", "integrity": "sha1-UwlCu1ilEvzK/lP+IQ8TolNV3Ig=", "dev": true, "optional": true, "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": ">=6"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "http://*************:4873/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/npm-run-path": {"version": "4.0.1", "resolved": "http://*************:4873/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "dev": true, "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "http://*************:4873/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "resolved": "http://*************:4873/onetime/-/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "dev": true, "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "http://*************:4873/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "dev": true, "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "http://*************:4873/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-locate/node_modules/p-limit": {"version": "2.3.0", "resolved": "http://*************:4873/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-map": {"version": "4.0.0", "resolved": "http://*************:4873/p-map/-/p-map-4.0.0.tgz", "integrity": "sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=", "dev": true, "optional": true, "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "http://*************:4873/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "http://*************:4873/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "dev": true, "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "http://*************:4873/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "http://*************:4873/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "http://*************:4873/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "http://*************:4873/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true}, "node_modules/pg-connection-string": {"version": "2.6.0", "resolved": "https://registry.npmmirror.com/pg-connection-string/-/pg-connection-string-2.6.0.tgz", "integrity": "sha512-x14ibktcwlHKoHxx9X3uTVW9zIGR41ZB6QNhHb21OPNdCCO3NaRnpJuwKIQSR4u+Yqjx4HCvy7Hh7VSy1U4dGg==", "dev": true}, "node_modules/picocolors": {"version": "1.0.0", "resolved": "http://*************:4873/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=", "dev": true}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "http://*************:4873/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pirates": {"version": "4.0.6", "resolved": "http://*************:4873/pirates/-/pirates-4.0.6.tgz", "integrity": "sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==", "dev": true, "engines": {"node": ">= 6"}}, "node_modules/pkg-dir": {"version": "4.2.0", "resolved": "http://*************:4873/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/prebuild-install": {"version": "7.1.2", "resolved": "http://*************:4873/prebuild-install/-/prebuild-install-7.1.2.tgz", "integrity": "sha512-UnNke3IQb6sgarcZIDU3gbMeTp/9SSU1DAIkil7PrqG1vZlBtY5msYccSKSHDqa3hNg436IXK+SNImReuA1wEQ==", "dev": true, "dependencies": {"detect-libc": "^2.0.0", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.3", "mkdirp-classic": "^0.5.3", "napi-build-utils": "^1.0.1", "node-abi": "^3.3.0", "pump": "^3.0.0", "rc": "^1.2.7", "simple-get": "^4.0.0", "tar-fs": "^2.0.0", "tunnel-agent": "^0.6.0"}, "bin": {"prebuild-install": "bin.js"}, "engines": {"node": ">=10"}}, "node_modules/pretty-format": {"version": "29.6.1", "resolved": "http://*************:4873/pretty-format/-/pretty-format-29.6.1.tgz", "integrity": "sha512-7jRj+yXO0W7e4/tSJKoR7HRIHLPPjtNaUGG2xxKQnGvPNRkgWcQ0AZX6P4KBRJN4FcTBWb3sa7DVUJmocYuoog==", "dev": true, "dependencies": {"@jest/schemas": "^29.6.0", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "resolved": "http://*************:4873/ansi-styles/-/ansi-styles-5.2.0.tgz", "integrity": "sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/promise-inflight": {"version": "1.0.1", "resolved": "http://*************:4873/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "dev": true, "optional": true}, "node_modules/promise-retry": {"version": "2.0.1", "resolved": "http://*************:4873/promise-retry/-/promise-retry-2.0.1.tgz", "integrity": "sha1-/3R6E2IKtXumiPX8Z4VUEMNw2iI=", "dev": true, "optional": true, "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}, "node_modules/prompts": {"version": "2.4.2", "resolved": "http://*************:4873/prompts/-/prompts-2.4.2.tgz", "integrity": "sha1-e1fnOzpIAprRDr1E90sBcipMsGk=", "dev": true, "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/pump": {"version": "3.0.2", "resolved": "http://*************:4873/pump/-/pump-3.0.2.tgz", "integrity": "sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==", "dev": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/pure-rand": {"version": "6.0.2", "resolved": "http://*************:4873/pure-rand/-/pure-rand-6.0.2.tgz", "integrity": "sha512-6Yg0ekpKICSjPswYOuC5sku/TSWaRYlA0qsXqJgM/d/4pLPHPuTxK7Nbf7jFKzAeedUhR8C7K9Uv63FBsSo8xQ==", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/dubzzz"}, {"type": "opencollective", "url": "https://opencollective.com/fast-check"}]}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "http://*************:4873/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/rc": {"version": "1.2.8", "resolved": "http://*************:4873/rc/-/rc-1.2.8.tgz", "integrity": "sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=", "dev": true, "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/rc/node_modules/strip-json-comments": {"version": "2.0.1", "resolved": "http://*************:4873/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/react-is": {"version": "18.2.0", "resolved": "http://*************:4873/react-is/-/react-is-18.2.0.tgz", "integrity": "sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==", "dev": true}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "http://*************:4873/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "dev": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/regenerator-runtime": {"version": "0.14.1", "resolved": "http://*************:4873/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==", "dev": true}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "http://*************:4873/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve": {"version": "1.22.3", "resolved": "http://*************:4873/resolve/-/resolve-1.22.3.tgz", "integrity": "sha512-P8ur/gp/AmbEzjr729bZnLjXK5Z+4P0zhIJgBgzqRih7hL7BOukHGtSTA3ACMY467GRFz3duQsi0bDZdR7DKdw==", "dev": true, "dependencies": {"is-core-module": "^2.12.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "3.0.0", "resolved": "http://*************:4873/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "integrity": "sha1-DwB18bslRHZs9zumpuKt/ryxPy0=", "dev": true, "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "5.0.0", "resolved": "http://*************:4873/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/resolve.exports": {"version": "2.0.2", "resolved": "http://*************:4873/resolve.exports/-/resolve.exports-2.0.2.tgz", "integrity": "sha512-X2UW6Nw3n/aMgDVy+0rSqgHlv39WZAlZrXCdnbyEiKm17DSqHX4MmQMaST3FbeWR5FTuRcUwYAziZajji0Y7mg==", "dev": true, "engines": {"node": ">=10"}}, "node_modules/retry": {"version": "0.12.0", "resolved": "http://*************:4873/retry/-/retry-0.12.0.tgz", "integrity": "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=", "dev": true, "optional": true, "engines": {"node": ">= 4"}}, "node_modules/retry-as-promised": {"version": "7.0.4", "resolved": "https://registry.npmmirror.com/retry-as-promised/-/retry-as-promised-7.0.4.tgz", "integrity": "sha512-XgmCoxKWkDofwH8WddD0w85ZfqYz+ZHlr5yo+3YUCfycWawU56T5ckWXsScsj5B8tqUcIG67DxXByo3VUgiAdA==", "dev": true}, "node_modules/reusify": {"version": "1.0.4", "resolved": "http://*************:4873/reusify/-/reusify-1.0.4.tgz", "integrity": "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==", "dev": true, "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "http://*************:4873/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "dev": true, "optional": true, "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "http://*************:4873/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "http://*************:4873/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://*************:4873/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true, "optional": true}, "node_modules/semver": {"version": "7.5.4", "resolved": "http://*************:4873/semver/-/semver-7.5.4.tgz", "integrity": "sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==", "dev": true, "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/sequelize": {"version": "6.32.1", "resolved": "http://*************:4873/sequelize/-/sequelize-6.32.1.tgz", "integrity": "sha512-3Iv0jruv57Y0YvcxQW7BE56O7DC1BojcfIrqh6my+IQwde+9u/YnuYHzK+8kmZLhLvaziRT1eWu38nh9yVwn/g==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/sequelize"}], "dependencies": {"@types/debug": "^4.1.8", "@types/validator": "^13.7.17", "debug": "^4.3.4", "dottie": "^2.0.4", "inflection": "^1.13.4", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pg-connection-string": "^2.6.0", "retry-as-promised": "^7.0.4", "semver": "^7.5.1", "sequelize-pool": "^7.1.0", "toposort-class": "^1.0.1", "uuid": "^8.3.2", "validator": "^13.9.0", "wkx": "^0.5.0"}, "engines": {"node": ">=10.0.0"}, "peerDependenciesMeta": {"ibm_db": {"optional": true}, "mariadb": {"optional": true}, "mysql2": {"optional": true}, "oracledb": {"optional": true}, "pg": {"optional": true}, "pg-hstore": {"optional": true}, "snowflake-sdk": {"optional": true}, "sqlite3": {"optional": true}, "tedious": {"optional": true}}}, "node_modules/sequelize-pool": {"version": "7.1.0", "resolved": "https://registry.npmmirror.com/sequelize-pool/-/sequelize-pool-7.1.0.tgz", "integrity": "sha512-G9c0qlIWQSK29pR/5U2JF5dDQeqqHRragoyahj/Nx4KOOQ3CPPfzxnfqFPCSB7x5UgjOgnZ61nSxz+fjDpRlJg==", "dev": true, "engines": {"node": ">= 10.0.0"}}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "http://*************:4873/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true, "optional": true}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "http://*************:4873/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "http://*************:4873/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "http://*************:4873/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "dev": true}, "node_modules/simple-concat": {"version": "1.0.1", "resolved": "http://*************:4873/simple-concat/-/simple-concat-1.0.1.tgz", "integrity": "sha1-9Gl2CCujXCJj8cirXt/ibEHJVS8=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/simple-get": {"version": "4.0.1", "resolved": "http://*************:4873/simple-get/-/simple-get-4.0.1.tgz", "integrity": "sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"decompress-response": "^6.0.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "node_modules/sisteransi": {"version": "1.0.5", "resolved": "http://*************:4873/sisteransi/-/sisteransi-1.0.5.tgz", "integrity": "sha1-E01oEpd1ZDfMBcoBNw06elcQde0=", "dev": true}, "node_modules/slash": {"version": "3.0.0", "resolved": "http://*************:4873/slash/-/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/smart-buffer": {"version": "4.2.0", "resolved": "http://*************:4873/smart-buffer/-/smart-buffer-4.2.0.tgz", "integrity": "sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=", "dev": true, "optional": true, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks": {"version": "2.7.2", "resolved": "http://*************:4873/socks/-/socks-2.7.1.tgz", "integrity": "sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==", "dev": true, "optional": true, "dependencies": {"ip": "^2.0.0", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.13.0", "npm": ">= 3.0.0"}}, "node_modules/socks-proxy-agent": {"version": "6.2.1", "resolved": "http://*************:4873/socks-proxy-agent/-/socks-proxy-agent-6.2.1.tgz", "integrity": "sha512-a6KW9G+6B3nWZ1yB8G7pJwL3ggLy1uTzKAgCb7ttblwqdz9fMGJUuTy3uFzEP48FAs9FLILlmzDlE2JJhVQaXQ==", "dev": true, "optional": true, "dependencies": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}, "engines": {"node": ">= 10"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "http://*************:4873/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.13", "resolved": "http://*************:4873/source-map-support/-/source-map-support-0.5.13.tgz", "integrity": "sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=", "dev": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "http://*************:4873/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}, "node_modules/sqlite3": {"version": "5.1.7", "resolved": "http://*************:4873/sqlite3/-/sqlite3-5.1.7.tgz", "integrity": "sha512-GGIyOiFaG+TUra3JIfkI/zGP8yZYLPQ0pl1bH+ODjiX57sPhrLU5sQJn1y9bDKZUFYkX1crlrPfSYt0BKKdkog==", "dev": true, "hasInstallScript": true, "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1", "tar": "^6.1.11"}, "optionalDependencies": {"node-gyp": "8.x"}, "peerDependencies": {"node-gyp": "8.x"}, "peerDependenciesMeta": {"node-gyp": {"optional": true}}}, "node_modules/ssri": {"version": "8.0.1", "resolved": "http://*************:4873/ssri/-/ssri-8.0.1.tgz", "integrity": "sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=", "dev": true, "optional": true, "dependencies": {"minipass": "^3.1.1"}, "engines": {"node": ">= 8"}}, "node_modules/stack-utils": {"version": "2.0.6", "resolved": "http://*************:4873/stack-utils/-/stack-utils-2.0.6.tgz", "integrity": "sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==", "dev": true, "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "http://*************:4873/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "dev": true, "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-length": {"version": "4.0.2", "resolved": "http://*************:4873/string-length/-/string-length-4.0.2.tgz", "integrity": "sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=", "dev": true, "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "http://*************:4873/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://*************:4873/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "4.0.0", "resolved": "http://*************:4873/strip-bom/-/strip-bom-4.0.0.tgz", "integrity": "sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "resolved": "http://*************:4873/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "http://*************:4873/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "http://*************:4873/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://*************:4873/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tar": {"version": "6.1.15", "resolved": "http://*************:4873/tar/-/tar-6.1.15.tgz", "integrity": "sha512-/zKt9UyngnxIT/EAGYuxaMYgOIJiP81ab9ZfkILq4oNLPFX50qyYmu7jRj9qeXoxmJHjGlbH0+cm2uy1WCs10A==", "dev": true, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar-fs": {"version": "2.1.1", "resolved": "http://*************:4873/tar-fs/-/tar-fs-2.1.1.tgz", "integrity": "sha512-V0r2Y9scmbDRLCNex/+hYzvp/zyYjvFbHPNgVTKfQvVrb6guiE/fxP+XblDNR011utopbkex2nM4dHNV6GDsng==", "dev": true, "dependencies": {"chownr": "^1.1.1", "mkdirp-classic": "^0.5.2", "pump": "^3.0.0", "tar-stream": "^2.1.4"}}, "node_modules/tar-fs/node_modules/chownr": {"version": "1.1.4", "resolved": "http://*************:4873/chownr/-/chownr-1.1.4.tgz", "integrity": "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==", "dev": true}, "node_modules/tar-stream": {"version": "2.2.0", "resolved": "http://*************:4873/tar-stream/-/tar-stream-2.2.0.tgz", "integrity": "sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==", "dev": true, "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/tar/node_modules/minipass": {"version": "5.0.0", "resolved": "http://*************:4873/minipass/-/minipass-5.0.0.tgz", "integrity": "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/test-exclude": {"version": "6.0.0", "resolved": "http://*************:4873/test-exclude/-/test-exclude-6.0.0.tgz", "integrity": "sha1-BKhphmHYBepvopO2y55jrARO8V4=", "dev": true, "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/tmpl": {"version": "1.0.5", "resolved": "http://*************:4873/tmpl/-/tmpl-1.0.5.tgz", "integrity": "sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=", "dev": true}, "node_modules/to-fast-properties": {"version": "2.0.0", "resolved": "http://*************:4873/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "http://*************:4873/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toposort-class": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/toposort-class/-/toposort-class-1.0.1.tgz", "integrity": "sha512-OsLcGGbYF3rMjPUf8oKktyvCiUxSbqMMS39m33MAjLTC1DVIH6x3WSt63/M77ihI09+Sdfk1AXvfhCEeUmC7mg==", "dev": true}, "node_modules/ts-jest": {"version": "29.1.1", "resolved": "http://*************:4873/ts-jest/-/ts-jest-29.1.1.tgz", "integrity": "sha512-D6xjnnbP17cC85nliwGiL+tpoKN0StpgE0TeOjXQTU6MVCfsB4v7aW05CgQ/1OywGb0x/oy9hHFnN+sczTiRaA==", "dev": true, "dependencies": {"bs-logger": "0.x", "fast-json-stable-stringify": "2.x", "jest-util": "^29.0.0", "json5": "^2.2.3", "lodash.memoize": "4.x", "make-error": "1.x", "semver": "^7.5.3", "yargs-parser": "^21.0.1"}, "bin": {"ts-jest": "cli.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.0 <8", "@jest/types": "^29.0.0", "babel-jest": "^29.0.0", "jest": "^29.0.0", "typescript": ">=4.3 <6"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "@jest/types": {"optional": true}, "babel-jest": {"optional": true}, "esbuild": {"optional": true}}}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "http://*************:4873/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "dev": true, "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/type-detect": {"version": "4.0.8", "resolved": "http://*************:4873/type-detect/-/type-detect-4.0.8.tgz", "integrity": "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.21.3", "resolved": "http://*************:4873/type-fest/-/type-fest-0.21.3.tgz", "integrity": "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typescript": {"version": "5.1.6", "resolved": "http://*************:4873/typescript/-/typescript-5.1.6.tgz", "integrity": "sha512-zaWCozRZ6DLEWAWFrVDz1H6FVXzUSfTy5FUMWsQlU8Ym5JP9eO4xkTIROFCQvhQf61z6O/G6ugw3SgAnvvm+HA==", "dev": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/unique-filename": {"version": "1.1.1", "resolved": "http://*************:4873/unique-filename/-/unique-filename-1.1.1.tgz", "integrity": "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=", "dev": true, "optional": true, "dependencies": {"unique-slug": "^2.0.0"}}, "node_modules/unique-slug": {"version": "2.0.2", "resolved": "http://*************:4873/unique-slug/-/unique-slug-2.0.2.tgz", "integrity": "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=", "dev": true, "optional": true, "dependencies": {"imurmurhash": "^0.1.4"}}, "node_modules/update-browserslist-db": {"version": "1.0.11", "resolved": "http://*************:4873/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz", "integrity": "sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://*************:4873/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "dev": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-to-istanbul": {"version": "9.1.0", "resolved": "http://*************:4873/v8-to-istanbul/-/v8-to-istanbul-9.1.0.tgz", "integrity": "sha512-6z3GW9x8G1gd+JIIgQQQxXuiJtCXeAjp6RaPEPLv62mH3iPHPxV6W3robxtCzNErRo6ZwTmzWhsbNvjyEBKzKA==", "dev": true, "dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^1.6.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/v8-to-istanbul/node_modules/convert-source-map": {"version": "1.9.0", "resolved": "http://*************:4873/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==", "dev": true}, "node_modules/validator": {"version": "13.12.0", "resolved": "http://*************:4873/validator/-/validator-13.12.0.tgz", "integrity": "sha512-c1Q0mCiPlgdTVVVIJIrBuxNicYE+t/7oKeI9MWLj3fh/uq2Pxh/3eeWbVZ4OcGW1TUf53At0njHw5SMdA3tmMg==", "dev": true, "engines": {"node": ">= 0.10"}}, "node_modules/walker": {"version": "1.0.8", "resolved": "http://*************:4873/walker/-/walker-1.0.8.tgz", "integrity": "sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==", "dev": true, "dependencies": {"makeerror": "1.0.12"}}, "node_modules/which": {"version": "2.0.2", "resolved": "http://*************:4873/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wide-align": {"version": "1.1.5", "resolved": "http://*************:4873/wide-align/-/wide-align-1.1.5.tgz", "integrity": "sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==", "dev": true, "optional": true, "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "node_modules/wkx": {"version": "0.5.0", "resolved": "https://registry.npmmirror.com/wkx/-/wkx-0.5.0.tgz", "integrity": "sha512-Xng/d4Ichh8uN4l0FToV/258EjMGU9MGcA0HV2d9B/ZpZB3lqQm7nkOdZdm5GhKtLLhAE7PiVQwN4eN+2YJJUg==", "dev": true, "dependencies": {"@types/node": "*"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "http://*************:4873/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "http://*************:4873/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "node_modules/write-file-atomic": {"version": "4.0.2", "resolved": "http://*************:4873/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "integrity": "sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==", "dev": true, "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "http://*************:4873/y18n/-/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "dev": true, "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "dev": true}, "node_modules/yargs": {"version": "17.7.2", "resolved": "http://*************:4873/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "dev": true, "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "http://*************:4873/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "dev": true, "engines": {"node": ">=12"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "http://*************:4873/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}, "dependencies": {"@ampproject/remapping": {"version": "2.2.1", "resolved": "http://*************:4873/@ampproject/remapping/-/remapping-2.2.1.tgz", "integrity": "sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "@babel/code-frame": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/code-frame/-/code-frame-7.22.5.tgz", "integrity": "sha512-Xmwn266vad+6DAqEB2A6V/CcZVp62BbwVmcOJc2RPuwih1kw02TjQvWVWlcKGbBPd+8/0V5DEkOcizRGYsspYQ==", "dev": true, "requires": {"@babel/highlight": "^7.22.5"}}, "@babel/compat-data": {"version": "7.22.6", "resolved": "http://*************:4873/@babel/compat-data/-/compat-data-7.22.6.tgz", "integrity": "sha512-29tfsWTq2Ftu7MXmimyC0C5FDZv5DYxOZkh3XD3+QW4V/BYuv/LyEsjj3c0hqedEaDt6DBfDvexMKU8YevdqFg==", "dev": true}, "@babel/core": {"version": "7.22.8", "resolved": "http://*************:4873/@babel/core/-/core-7.22.8.tgz", "integrity": "sha512-75+KxFB4CZqYRXjx4NlR4J7yGvKumBuZTmV4NV6v09dVXXkuYVYLT68N6HCzLvfJ+fWCxQsntNzKwwIXL4bHnw==", "dev": true, "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.22.5", "@babel/generator": "^7.22.7", "@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-module-transforms": "^7.22.5", "@babel/helpers": "^7.22.6", "@babel/parser": "^7.22.7", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.8", "@babel/types": "^7.22.5", "@nicolo-ribaudo/semver-v6": "^6.3.3", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.2"}, "dependencies": {"convert-source-map": {"version": "1.9.0", "resolved": "http://*************:4873/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==", "dev": true}}}, "@babel/generator": {"version": "7.22.7", "resolved": "http://*************:4873/@babel/generator/-/generator-7.22.7.tgz", "integrity": "sha512-p+jPjMG+SI8yvIaxGgeW24u7q9+5+TGpZh8/CuB7RhBKd7RCy8FayNEFNNKrNK/eUcY/4ExQqLmyrvBXKsIcwQ==", "dev": true, "requires": {"@babel/types": "^7.22.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}}, "@babel/helper-compilation-targets": {"version": "7.22.6", "resolved": "http://*************:4873/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.6.tgz", "integrity": "sha512-534sYEqWD9VfUm3IPn2SLcH4Q3P86XL+QvqdC7ZsFrzyyPF3T4XGiVghF6PTYNdWg6pXuoqXxNQAhbYeEInTzA==", "dev": true, "requires": {"@babel/compat-data": "^7.22.6", "@babel/helper-validator-option": "^7.22.5", "@nicolo-ribaudo/semver-v6": "^6.3.3", "browserslist": "^4.21.9", "lru-cache": "^5.1.1"}, "dependencies": {"lru-cache": {"version": "5.1.1", "resolved": "http://*************:4873/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "requires": {"yallist": "^3.0.2"}}, "yallist": {"version": "3.1.1", "resolved": "http://*************:4873/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true}}}, "@babel/helper-environment-visitor": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.5.tgz", "integrity": "sha512-XGmhECfVA/5sAt+H+xpSg0mfrHq6FzNr9Oxh7PSEBBRUb/mL7Kz3NICXb194rCqAEdxkhPT1a88teizAFyvk8Q==", "dev": true}, "@babel/helper-function-name": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-function-name/-/helper-function-name-7.22.5.tgz", "integrity": "sha512-wtHSq6jMRE3uF2otvfuD3DIvVhOsSNshQl0Qrd7qC9oQJzHvOL4qQXlQn2916+CXGywIjpGuIkoyZRRxHPiNQQ==", "dev": true, "requires": {"@babel/template": "^7.22.5", "@babel/types": "^7.22.5"}}, "@babel/helper-hoist-variables": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz", "integrity": "sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==", "dev": true, "requires": {"@babel/types": "^7.22.5"}}, "@babel/helper-module-imports": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-module-imports/-/helper-module-imports-7.22.5.tgz", "integrity": "sha512-8Dl6+HD/cKifutF5qGd/8ZJi84QeAKh+CEe1sBzz8UayBBGg1dAIJrdHOcOM5b2MpzWL2yuotJTtGjETq0qjXg==", "dev": true, "requires": {"@babel/types": "^7.22.5"}}, "@babel/helper-module-transforms": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-module-transforms/-/helper-module-transforms-7.22.5.tgz", "integrity": "sha512-+hGKDt/Ze8GFExiVHno/2dvG5IdstpzCq0y4Qc9OJ25D4q3pKfiIP/4Vp3/JvhDkLKsDK2api3q3fpIgiIF5bw==", "dev": true, "requires": {"@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/helper-simple-access": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/types": "^7.22.5"}}, "@babel/helper-plugin-utils": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-plugin-utils/-/helper-plugin-utils-7.22.5.tgz", "integrity": "sha512-uLls06UVKgFG9QD4OeFYLEGteMIAa5kpTPcFL28yuCIIzsf6ZyKZMllKVOCZFhiZ5ptnwX4mtKdWCBE/uT4amg==", "dev": true}, "@babel/helper-simple-access": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz", "integrity": "sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==", "dev": true, "requires": {"@babel/types": "^7.22.5"}}, "@babel/helper-split-export-declaration": {"version": "7.22.6", "resolved": "http://*************:4873/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz", "integrity": "sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==", "dev": true, "requires": {"@babel/types": "^7.22.5"}}, "@babel/helper-string-parser": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz", "integrity": "sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==", "dev": true}, "@babel/helper-validator-identifier": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz", "integrity": "sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==", "dev": true}, "@babel/helper-validator-option": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/helper-validator-option/-/helper-validator-option-7.22.5.tgz", "integrity": "sha512-R3oB6xlIVKUnxNUxbmgq7pKjxpru24zlimpE8WK47fACIlM0II/Hm1RS8IaOI7NgCr6LNS+jl5l75m20npAziw==", "dev": true}, "@babel/helpers": {"version": "7.22.6", "resolved": "http://*************:4873/@babel/helpers/-/helpers-7.22.6.tgz", "integrity": "sha512-YjDs6y/fVOYFV8hAf1rxd1QvR9wJe1pDBZ2AREKq/SDayfPzgk0PBnVuTCE5X1acEpMMNOVUqoe+OwiZGJ+OaA==", "dev": true, "requires": {"@babel/template": "^7.22.5", "@babel/traverse": "^7.22.6", "@babel/types": "^7.22.5"}}, "@babel/highlight": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/highlight/-/highlight-7.22.5.tgz", "integrity": "sha512-BSKlD1hgnedS5XRnGOljZawtag7H1yPfQp0tdNJCHoH6AZ+Pcm9VvkrK59/Yy593Ypg0zMxH2BxD1VPYUQ7UIw==", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.22.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "http://*************:4873/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "http://*************:4873/chalk/-/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://*************:4873/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://*************:4873/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://*************:4873/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true}, "has-flag": {"version": "3.0.0", "resolved": "http://*************:4873/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "http://*************:4873/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "@babel/parser": {"version": "7.22.7", "resolved": "http://*************:4873/@babel/parser/-/parser-7.22.7.tgz", "integrity": "sha512-7NF8pOkHP5o2vpmGgNGcfAeCvOYhGLyA3Z4eBQkT1RJlWu47n63bCs93QfJ2hIAFCil7L5P2IWhs1oToVgrL0Q==", "dev": true}, "@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "http://*************:4873/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-bigint": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "integrity": "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "http://*************:4873/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.12.13"}}, "@babel/plugin-syntax-import-meta": {"version": "7.10.4", "resolved": "http://*************:4873/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "integrity": "sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-jsx": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.22.5.tgz", "integrity": "sha512-gvyP4hZrgrs/wWMaocvxZ44Hw0b3W8Pe+cMxc8V1ULQ07oh8VNbIRaoD1LRZVTvD+0nieDKjfgKg89sD7rrKrg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.22.5"}}, "@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "http://*************:4873/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha1-ypHvRjA1MESLkGZSusLp/plB9pk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "http://*************:4873/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "http://*************:4873/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "http://*************:4873/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-typescript": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.22.5.tgz", "integrity": "sha512-1mS2o03i7t1c6VzH6fdQ3OA8tcEIxwG18zIPRp+UY1Ihv6W+XZzBCVxExF9upussPXJ0xE9XRHwMoNs1ep/nRQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.22.5"}}, "@babel/runtime": {"version": "7.24.7", "resolved": "http://*************:4873/@babel/runtime/-/runtime-7.24.7.tgz", "integrity": "sha512-UwgBRMjJP+xv857DCngvqXI3Iq6J4v0wXmwc6sapg+zyhbwmQX67LUEFrkK5tbyJ30jGuG3ZvWpBiB9LCy1kWw==", "dev": true, "requires": {"regenerator-runtime": "^0.14.0"}}, "@babel/template": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/template/-/template-7.22.5.tgz", "integrity": "sha512-X7yV7eiwAxdj9k94NEylvbVHLiVG1nvzCV2EAowhxLTwODV1jl9UzZ48leOC0sH7OnuHrIkllaBgneUykIcZaw==", "dev": true, "requires": {"@babel/code-frame": "^7.22.5", "@babel/parser": "^7.22.5", "@babel/types": "^7.22.5"}}, "@babel/traverse": {"version": "7.22.8", "resolved": "http://*************:4873/@babel/traverse/-/traverse-7.22.8.tgz", "integrity": "sha512-y6LPR+wpM2I3qJrsheCTwhIinzkETbplIgPBbwvqPKc+uljeA5gP+3nP8irdYt1mjQaDnlIcG+dw8OjAco4GXw==", "dev": true, "requires": {"@babel/code-frame": "^7.22.5", "@babel/generator": "^7.22.7", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/parser": "^7.22.7", "@babel/types": "^7.22.5", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.22.5", "resolved": "http://*************:4873/@babel/types/-/types-7.22.5.tgz", "integrity": "sha512-zo3MIHGOkPOfoRXitsgHLjEXmlDaD/5KU1Uzuc9GNiZPhSqVxVRtxuPaSBZDsYZ9qV88AjtMtWW7ww98loJ9KA==", "dev": true, "requires": {"@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "to-fast-properties": "^2.0.0"}}, "@bcoe/v8-coverage": {"version": "0.2.3", "resolved": "http://*************:4873/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "integrity": "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==", "dev": true}, "@gar/promisify": {"version": "1.1.3", "resolved": "http://*************:4873/@gar/promisify/-/promisify-1.1.3.tgz", "integrity": "sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==", "dev": true, "optional": true}, "@istanbuljs/load-nyc-config": {"version": "1.1.0", "resolved": "http://*************:4873/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "integrity": "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==", "dev": true, "requires": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}}, "@istanbuljs/schema": {"version": "0.1.3", "resolved": "http://*************:4873/@istanbuljs/schema/-/schema-0.1.3.tgz", "integrity": "sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=", "dev": true}, "@jest/console": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/console/-/console-29.6.1.tgz", "integrity": "sha512-Aj772AYgwTSr5w8qnyoJ0eDYvN6bMsH3ORH1ivMotrInHLKdUz6BDlaEXHdM6kODaBIkNIyQGzsMvRdOv7VG7Q==", "dev": true, "requires": {"@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^29.6.1", "jest-util": "^29.6.1", "slash": "^3.0.0"}}, "@jest/core": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/core/-/core-29.6.1.tgz", "integrity": "sha512-CcowHypRSm5oYQ1obz1wfvkjZZ2qoQlrKKvlfPwh5jUXVU12TWr2qMeH8chLMuTFzHh5a1g2yaqlqDICbr+ukQ==", "dev": true, "requires": {"@jest/console": "^29.6.1", "@jest/reporters": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^29.5.0", "jest-config": "^29.6.1", "jest-haste-map": "^29.6.1", "jest-message-util": "^29.6.1", "jest-regex-util": "^29.4.3", "jest-resolve": "^29.6.1", "jest-resolve-dependencies": "^29.6.1", "jest-runner": "^29.6.1", "jest-runtime": "^29.6.1", "jest-snapshot": "^29.6.1", "jest-util": "^29.6.1", "jest-validate": "^29.6.1", "jest-watcher": "^29.6.1", "micromatch": "^4.0.4", "pretty-format": "^29.6.1", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}}, "@jest/environment": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/environment/-/environment-29.6.1.tgz", "integrity": "sha512-RMMXx4ws+Gbvw3DfLSuo2cfQlK7IwGbpuEWXCqyYDcqYTI+9Ju3a5hDnXaxjNsa6uKh9PQF2v+qg+RLe63tz5A==", "dev": true, "requires": {"@jest/fake-timers": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "jest-mock": "^29.6.1"}}, "@jest/expect": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/expect/-/expect-29.6.1.tgz", "integrity": "sha512-N5xlPrAYaRNyFgVf2s9Uyyvr795jnB6rObuPx4QFvNJz8aAjpZUDfO4bh5G/xuplMID8PrnuF1+SfSyDxhsgYg==", "dev": true, "requires": {"expect": "^29.6.1", "jest-snapshot": "^29.6.1"}}, "@jest/expect-utils": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/expect-utils/-/expect-utils-29.6.1.tgz", "integrity": "sha512-o319vIf5pEMx0LmzSxxkYYxo4wrRLKHq9dP1yJU7FoPTB0LfAKSz8SWD6D/6U3v/O52t9cF5t+MeJiRsfk7zMw==", "dev": true, "requires": {"jest-get-type": "^29.4.3"}}, "@jest/fake-timers": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/fake-timers/-/fake-timers-29.6.1.tgz", "integrity": "sha512-RdgHgbXyosCDMVYmj7lLpUwXA4c69vcNzhrt69dJJdf8azUrpRh3ckFCaTPNjsEeRi27Cig0oKDGxy5j7hOgHg==", "dev": true, "requires": {"@jest/types": "^29.6.1", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.6.1", "jest-mock": "^29.6.1", "jest-util": "^29.6.1"}}, "@jest/globals": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/globals/-/globals-29.6.1.tgz", "integrity": "sha512-2VjpaGy78JY9n9370H8zGRCFbYVWwjY6RdDMhoJHa1sYfwe6XM/azGN0SjY8kk7BOZApIejQ1BFPyH7FPG0w3A==", "dev": true, "requires": {"@jest/environment": "^29.6.1", "@jest/expect": "^29.6.1", "@jest/types": "^29.6.1", "jest-mock": "^29.6.1"}}, "@jest/reporters": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/reporters/-/reporters-29.6.1.tgz", "integrity": "sha512-9zuaI9QKr9JnoZtFQlw4GREQbxgmNYXU6QuWtmuODvk5nvPUeBYapVR/VYMyi2WSx3jXTLJTJji8rN6+Cm4+FA==", "dev": true, "requires": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@jridgewell/trace-mapping": "^0.3.18", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^5.1.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-message-util": "^29.6.1", "jest-util": "^29.6.1", "jest-worker": "^29.6.1", "slash": "^3.0.0", "string-length": "^4.0.1", "strip-ansi": "^6.0.0", "v8-to-istanbul": "^9.0.1"}}, "@jest/schemas": {"version": "29.6.0", "resolved": "http://*************:4873/@jest/schemas/-/schemas-29.6.0.tgz", "integrity": "sha512-rxLjXyJBTL4LQeJW3aKo0M/+GkCOXsO+8i9Iu7eDb6KwtP65ayoDsitrdPBtujxQ88k4wI2FNYfa6TOGwSn6cQ==", "dev": true, "requires": {"@sinclair/typebox": "^0.27.8"}}, "@jest/source-map": {"version": "29.6.0", "resolved": "http://*************:4873/@jest/source-map/-/source-map-29.6.0.tgz", "integrity": "sha512-oA+I2SHHQGxDCZpbrsCQSoMLb3Bz547JnM+jUr9qEbuw0vQlWZfpPS7CO9J7XiwKicEz9OFn/IYoLkkiUD7bzA==", "dev": true, "requires": {"@jridgewell/trace-mapping": "^0.3.18", "callsites": "^3.0.0", "graceful-fs": "^4.2.9"}}, "@jest/test-result": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/test-result/-/test-result-29.6.1.tgz", "integrity": "sha512-Ynr13ZRcpX6INak0TPUukU8GWRfm/vAytE3JbJNGAvINySWYdfE7dGZMbk36oVuK4CigpbhMn8eg1dixZ7ZJOw==", "dev": true, "requires": {"@jest/console": "^29.6.1", "@jest/types": "^29.6.1", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}}, "@jest/test-sequencer": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/test-sequencer/-/test-sequencer-29.6.1.tgz", "integrity": "sha512-oBkC36PCDf/wb6dWeQIhaviU0l5u6VCsXa119yqdUosYAt7/FbQU2M2UoziO3igj/HBDEgp57ONQ3fm0v9uyyg==", "dev": true, "requires": {"@jest/test-result": "^29.6.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "slash": "^3.0.0"}}, "@jest/transform": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/transform/-/transform-29.6.1.tgz", "integrity": "sha512-URnTneIU3ZjRSaf906cvf6Hpox3hIeJXRnz3VDSw5/X93gR8ycdfSIEy19FlVx8NFmpN7fe3Gb1xF+NjXaQLWg==", "dev": true, "requires": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.1", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "jest-regex-util": "^29.4.3", "jest-util": "^29.6.1", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}}, "@jest/types": {"version": "29.6.1", "resolved": "http://*************:4873/@jest/types/-/types-29.6.1.tgz", "integrity": "sha512-tPKQNMPuXgvdOn2/Lg9HNfUvjYVGolt04Hp03f5hAk878uwOLikN+JzeLY0HcVgKgFl9Hs3EIqpu3WX27XNhnw==", "dev": true, "requires": {"@jest/schemas": "^29.6.0", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}}, "@jridgewell/gen-mapping": {"version": "0.3.3", "resolved": "http://*************:4873/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz", "integrity": "sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "http://*************:4873/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true}, "@jridgewell/set-array": {"version": "1.1.2", "resolved": "http://*************:4873/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==", "dev": true}, "@jridgewell/sourcemap-codec": {"version": "1.4.15", "resolved": "http://*************:4873/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "integrity": "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.18", "resolved": "http://*************:4873/@jridgewell/trace-mapping/-/trace-mapping-0.3.18.tgz", "integrity": "sha512-w+niJYzMHdd7USdiH2U6869nqhD2nbfZXND5Yp93qIbEmnDNk7PD48o+YchRVpzMU7M6jVCbenTR7PA1FLQ9pA==", "dev": true, "requires": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}, "dependencies": {"@jridgewell/sourcemap-codec": {"version": "1.4.14", "resolved": "http://*************:4873/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "dev": true}}}, "@nicolo-ribaudo/semver-v6": {"version": "6.3.3", "resolved": "http://*************:4873/@nicolo-ribaudo/semver-v6/-/semver-v6-6.3.3.tgz", "integrity": "sha512-3Yc1fUTs69MG/uZbJlLSI3JISMn2UV2rg+1D/vROUqZyh3l6iYHCs7GMp+M40ZD7yOdDbYjJcU1oTJhrc+dGKg==", "dev": true}, "@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "http://*************:4873/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "resolved": "http://*************:4873/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true}, "@nodelib/fs.walk": {"version": "1.2.8", "resolved": "http://*************:4873/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@npmcli/fs": {"version": "1.1.1", "resolved": "http://*************:4873/@npmcli/fs/-/fs-1.1.1.tgz", "integrity": "sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==", "dev": true, "optional": true, "requires": {"@gar/promisify": "^1.0.1", "semver": "^7.3.5"}}, "@npmcli/move-file": {"version": "1.1.2", "resolved": "http://*************:4873/@npmcli/move-file/-/move-file-1.1.2.tgz", "integrity": "sha1-GoLD43L3yuklPrZtclQ9a4aFxnQ=", "dev": true, "optional": true, "requires": {"mkdirp": "^1.0.4", "rimraf": "^3.0.2"}}, "@sequelize/core": {"version": "7.0.0-alpha.42", "resolved": "http://*************:4873/@sequelize/core/-/core-7.0.0-alpha.42.tgz", "integrity": "sha512-rjj7wxVkOiPIuDzwR5AMIPBfNjFRwVpYUCwQ3j3ovtw8UPM1WSNECC1WKkF3SoX9cKZpDgYuQsDzEIontDo3vg==", "dev": true, "requires": {"@sequelize/utils": "7.0.0-alpha.42", "@types/debug": "^4.1.12", "@types/validator": "^13.11.9", "ansis": "^3.2.0", "bnf-parser": "^3.1.6", "dayjs": "^1.11.10", "debug": "^4.3.4", "dottie": "^2.0.6", "fast-glob": "^3.3.2", "inflection": "^3.0.0", "lodash": "^4.17.21", "retry-as-promised": "^7.0.4", "semver": "^7.3", "sequelize-pool": "^8.0.0", "toposort-class": "^1.0.1", "type-fest": "^4.14.0", "uuid": "^10.0.0", "validator": "^13.11.0"}, "dependencies": {"inflection": {"version": "3.0.0", "resolved": "http://*************:4873/inflection/-/inflection-3.0.0.tgz", "integrity": "sha512-1zEJU1l19SgJlmwqsEyFTbScw/tkMHFenUo//Y0i+XEP83gDFdMvPizAD/WGcE+l1ku12PcTVHQhO6g5E0UCMw==", "dev": true}, "sequelize-pool": {"version": "8.0.0", "resolved": "http://*************:4873/sequelize-pool/-/sequelize-pool-8.0.0.tgz", "integrity": "sha512-xY04c5ctp6lKE4ZoSVo7YJHN5FHVhoYMoH2Z8jWIJG26c9kJSkIjql5HgD9XG5cwJbYMF0Ko2Fhgws20HC90Ug==", "dev": true}, "type-fest": {"version": "4.26.1", "resolved": "http://*************:4873/type-fest/-/type-fest-4.26.1.tgz", "integrity": "sha512-yOGpmOAL7CkKe/91I5O3gPICmJNLJ1G4zFYVAsRHg7M64biSnPtRj0WNQt++bRkjYOqjWXrhnUw1utzmVErAdg==", "dev": true}, "uuid": {"version": "10.0.0", "resolved": "http://*************:4873/uuid/-/uuid-10.0.0.tgz", "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==", "dev": true}}}, "@sequelize/sqlite3": {"version": "7.0.0-alpha.42", "resolved": "http://*************:4873/@sequelize/sqlite3/-/sqlite3-7.0.0-alpha.42.tgz", "integrity": "sha512-USYRE1wRK9d4cG8ukD6C4m13e8SoPApQ15qubhJ3yXFFXiABhidKER6f9a4GE8GIhCuZlZkAyG+uws0qqn7qfQ==", "dev": true, "requires": {"@sequelize/core": "7.0.0-alpha.42", "@sequelize/utils": "7.0.0-alpha.42", "lodash": "^4.17.21", "sqlite3": "^5.1.7"}}, "@sequelize/utils": {"version": "7.0.0-alpha.42", "resolved": "http://*************:4873/@sequelize/utils/-/utils-7.0.0-alpha.42.tgz", "integrity": "sha512-O3iiv2OuolcpotOzEzwThEWIdicasXPAr64lWAuS4SRHzrFVufxiq5iuciKx+Lty0vyGIISqVjJRgkPkuq/2fQ==", "dev": true, "requires": {"@types/lodash": "^4.17.0", "lodash": "^4.17.21"}}, "@sinclair/typebox": {"version": "0.27.8", "resolved": "http://*************:4873/@sinclair/typebox/-/typebox-0.27.8.tgz", "integrity": "sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==", "dev": true}, "@sinonjs/commons": {"version": "3.0.0", "resolved": "http://*************:4873/@sinonjs/commons/-/commons-3.0.0.tgz", "integrity": "sha512-jXBtWAF4vmdNmZgD5FoKsVLv3rPgDnLgPbU84LIJ3otV44vJlDRokVng5v8NFJdCf/da9legHcKaRuZs4L7faA==", "dev": true, "requires": {"type-detect": "4.0.8"}}, "@sinonjs/fake-timers": {"version": "10.3.0", "resolved": "http://*************:4873/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "integrity": "sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==", "dev": true, "requires": {"@sinonjs/commons": "^3.0.0"}}, "@tootallnate/once": {"version": "1.1.2", "resolved": "http://*************:4873/@tootallnate/once/-/once-1.1.2.tgz", "integrity": "sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==", "dev": true, "optional": true}, "@types/babel__core": {"version": "7.20.1", "resolved": "http://*************:4873/@types/babel__core/-/babel__core-7.20.1.tgz", "integrity": "sha512-aACu/U/omhdk15O4Nfb+fHgH/z3QsfQzpnvRZhYhThms83ZnAOZz7zZAWO7mn2yyNQaA4xTO8GLK3uqFU4bYYw==", "dev": true, "requires": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "@types/babel__generator": {"version": "7.6.4", "resolved": "http://*************:4873/@types/babel__generator/-/babel__generator-7.6.4.tgz", "integrity": "sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@types/babel__template": {"version": "7.4.1", "resolved": "http://*************:4873/@types/babel__template/-/babel__template-7.4.1.tgz", "integrity": "sha1-PRpI/Z1sDt/Vby/1eNrtSPNsiWk=", "dev": true, "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "@types/babel__traverse": {"version": "7.20.1", "resolved": "http://*************:4873/@types/babel__traverse/-/babel__traverse-7.20.1.tgz", "integrity": "sha512-MitHFXnhtgwsGZWtT68URpOvLN4EREih1u3QtQiN4VdAxWKRVvGCSvw/Qth0M0Qq3pJpnGOu5JaM/ydK7OGbqg==", "dev": true, "requires": {"@babel/types": "^7.20.7"}}, "@types/debug": {"version": "4.1.12", "resolved": "http://*************:4873/@types/debug/-/debug-4.1.12.tgz", "integrity": "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==", "dev": true, "requires": {"@types/ms": "*"}}, "@types/graceful-fs": {"version": "4.1.6", "resolved": "http://*************:4873/@types/graceful-fs/-/graceful-fs-4.1.6.tgz", "integrity": "sha512-Sig0SNORX9fdW+bQuTEovKj3uHcUL6LQKbCrrqb1X7J6/ReAbhCXRAhc+SMejhLELFj2QcyuxmUooZ4bt5ReSw==", "dev": true, "requires": {"@types/node": "*"}}, "@types/istanbul-lib-coverage": {"version": "2.0.4", "resolved": "http://*************:4873/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz", "integrity": "sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==", "dev": true}, "@types/istanbul-lib-report": {"version": "3.0.0", "resolved": "http://*************:4873/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "integrity": "sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==", "dev": true, "requires": {"@types/istanbul-lib-coverage": "*"}}, "@types/istanbul-reports": {"version": "3.0.1", "resolved": "http://*************:4873/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz", "integrity": "sha1-kVP+mLuivVZaY63ZQ21vDX+EaP8=", "dev": true, "requires": {"@types/istanbul-lib-report": "*"}}, "@types/jest": {"version": "29.5.3", "resolved": "http://*************:4873/@types/jest/-/jest-29.5.3.tgz", "integrity": "sha512-1Nq7YrO/vJE/FYnqYyw0FS8LdrjExSgIiHyKg7xPpn+yi8Q4huZryKnkJatN1ZRH89Kw2v33/8ZMB7DuZeSLlA==", "dev": true, "requires": {"expect": "^29.0.0", "pretty-format": "^29.0.0"}}, "@types/lodash": {"version": "4.17.9", "resolved": "http://*************:4873/@types/lodash/-/lodash-4.17.9.tgz", "integrity": "sha512-w9iWudx1XWOHW5lQRS9iKpK/XuRhnN+0T7HvdCCd802FYkT1AMTnxndJHGrNJwRoRHkslGr4S29tjm1cT7x/7w==", "dev": true}, "@types/ms": {"version": "0.7.31", "resolved": "http://*************:4873/@types/ms/-/ms-0.7.31.tgz", "integrity": "sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA==", "dev": true}, "@types/node": {"version": "20.4.1", "resolved": "http://*************:4873/@types/node/-/node-20.4.1.tgz", "integrity": "sha512-JIzsAvJeA/5iY6Y/OxZbv1lUcc8dNSE77lb2gnBH+/PJ3lFR1Ccvgwl5JWnHAkNHcRsT0TbpVOsiMKZ1F/yyJg==", "dev": true}, "@types/prettier": {"version": "2.7.3", "resolved": "http://*************:4873/@types/prettier/-/prettier-2.7.3.tgz", "integrity": "sha512-+68kP9yzs4LMp7VNh8gdzMSPZFL44MLGqiHWvttYJe+6qnuVr4Ek9wSBQoveqY/r+LwjCcU29kNVkidwim+kYA==", "dev": true}, "@types/stack-utils": {"version": "2.0.1", "resolved": "http://*************:4873/@types/stack-utils/-/stack-utils-2.0.1.tgz", "integrity": "sha1-IPGClPeX8iCbX2XI47XI6CYdEnw=", "dev": true}, "@types/validator": {"version": "13.12.2", "resolved": "http://*************:4873/@types/validator/-/validator-13.12.2.tgz", "integrity": "sha512-6SlHBzUW8Jhf3liqrGGXyTJSIFe4nqlJ5A5KaMZ2l/vbM3Wh3KSybots/wfWVzNLK4D1NZluDlSQIbIEPx6oyA==", "dev": true}, "@types/yargs": {"version": "17.0.24", "resolved": "http://*************:4873/@types/yargs/-/yargs-17.0.24.tgz", "integrity": "sha512-6i0aC7jV6QzQB8ne1joVZ0eSFIstHsCrobmOtghM11yGlH0j43FKL2UhWdELkyps0zuf7qVTUVCCR+tgSlyLLw==", "dev": true, "requires": {"@types/yargs-parser": "*"}}, "@types/yargs-parser": {"version": "21.0.0", "resolved": "http://*************:4873/@types/yargs-parser/-/yargs-parser-21.0.0.tgz", "integrity": "sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==", "dev": true}, "abbrev": {"version": "1.1.1", "resolved": "http://*************:4873/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=", "dev": true, "optional": true}, "agent-base": {"version": "6.0.2", "resolved": "http://*************:4873/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=", "dev": true, "optional": true, "requires": {"debug": "4"}}, "agentkeepalive": {"version": "4.3.0", "resolved": "http://*************:4873/agentkeepalive/-/agentkeepalive-4.3.0.tgz", "integrity": "sha512-7Epl1Blf4Sy37j4v9f9FjICCh4+KAQOyXgHEwlyBiAQLbhKdq/i2QQU3amQalS/wPhdPzDXPL5DMR5bkn+YeWg==", "dev": true, "optional": true, "requires": {"debug": "^4.1.0", "depd": "^2.0.0", "humanize-ms": "^1.2.1"}}, "aggregate-error": {"version": "3.1.0", "resolved": "http://*************:4873/aggregate-error/-/aggregate-error-3.1.0.tgz", "integrity": "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=", "dev": true, "optional": true, "requires": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}}, "ansi-escapes": {"version": "4.3.2", "resolved": "http://*************:4873/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "integrity": "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=", "dev": true, "requires": {"type-fest": "^0.21.3"}}, "ansi-regex": {"version": "5.0.1", "resolved": "http://*************:4873/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true}, "ansi-styles": {"version": "4.3.0", "resolved": "http://*************:4873/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "ansis": {"version": "3.3.2", "resolved": "http://*************:4873/ansis/-/ansis-3.3.2.tgz", "integrity": "sha512-cFthbBlt+Oi0i9Pv/j6YdVWJh54CtjGACaMPCIrEV4Ha7HWsIjXDwseYV79TIL0B4+KfSwD5S70PeQDkPUd1rA==", "dev": true}, "anymatch": {"version": "3.1.3", "resolved": "http://*************:4873/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "dev": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "aproba": {"version": "2.0.0", "resolved": "http://*************:4873/aproba/-/aproba-2.0.0.tgz", "integrity": "sha1-UlILiuW1aSFbNU78DKo/4eRaitw=", "dev": true, "optional": true}, "argparse": {"version": "1.0.10", "resolved": "http://*************:4873/argparse/-/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "dev": true, "requires": {"sprintf-js": "~1.0.2"}}, "babel-jest": {"version": "29.6.1", "resolved": "http://*************:4873/babel-jest/-/babel-jest-29.6.1.tgz", "integrity": "sha512-qu+3bdPEQC6KZSPz+4Fyjbga5OODNcp49j6GKzG1EKbkfyJBxEYGVUmVGpwCSeGouG52R4EgYMLb6p9YeEEQ4A==", "dev": true, "requires": {"@jest/transform": "^29.6.1", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.5.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}}, "babel-plugin-istanbul": {"version": "6.1.1", "resolved": "http://*************:4873/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "integrity": "sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}}, "babel-plugin-jest-hoist": {"version": "29.5.0", "resolved": "http://*************:4873/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.5.0.tgz", "integrity": "sha512-zSuuuAlTMT4mzLj2nPnUm6fsE6270vdOfnpbJ+RmruU75UhLFvL0N2NgI7xpeS7NaB6hGqmd5pVpGTDYvi4Q3w==", "dev": true, "requires": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}}, "babel-preset-current-node-syntax": {"version": "1.0.1", "resolved": "http://*************:4873/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz", "integrity": "sha1-tDmSObibKgEfndvj5PQB/EDP9zs=", "dev": true, "requires": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-top-level-await": "^7.8.3"}}, "babel-preset-jest": {"version": "29.5.0", "resolved": "http://*************:4873/babel-preset-jest/-/babel-preset-jest-29.5.0.tgz", "integrity": "sha512-JOMloxOqdiBSxMAzjRaH023/vvcaSaec49zvg+2LmNsktC7ei39LTJGw02J+9uUtTZUq6xbLyJ4dxe9sSmIuAg==", "dev": true, "requires": {"babel-plugin-jest-hoist": "^29.5.0", "babel-preset-current-node-syntax": "^1.0.0"}}, "balanced-match": {"version": "1.0.2", "resolved": "http://*************:4873/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true}, "base64-js": {"version": "1.5.1", "resolved": "http://*************:4873/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "dev": true}, "bindings": {"version": "1.5.0", "resolved": "http://*************:4873/bindings/-/bindings-1.5.0.tgz", "integrity": "sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==", "dev": true, "requires": {"file-uri-to-path": "1.0.0"}}, "bl": {"version": "4.1.0", "resolved": "http://*************:4873/bl/-/bl-4.1.0.tgz", "integrity": "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==", "dev": true, "requires": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "bnf-parser": {"version": "3.1.6", "resolved": "http://*************:4873/bnf-parser/-/bnf-parser-3.1.6.tgz", "integrity": "sha512-3x0ECh6CghmcAYnY6uiVAOfl263XkWffDq5fQS20ac3k0U7TE5rTWNXTnOTckgmfZc94iharwqCyoV8OAYxYoA==", "dev": true}, "brace-expansion": {"version": "1.1.11", "resolved": "http://*************:4873/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.2", "resolved": "http://*************:4873/braces/-/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "browserslist": {"version": "4.21.9", "resolved": "http://*************:4873/browserslist/-/browserslist-4.21.9.tgz", "integrity": "sha512-M0MFoZzbUrRU4KNfCrDLnvyE7gub+peetoTid3TBIqtunaDJyXlwhakT+/VkvSXcfIzFfK/nkCs4nmyTmxdNSg==", "dev": true, "requires": {"caniuse-lite": "^1.0.30001503", "electron-to-chromium": "^1.4.431", "node-releases": "^2.0.12", "update-browserslist-db": "^1.0.11"}}, "bs-logger": {"version": "0.2.6", "resolved": "http://*************:4873/bs-logger/-/bs-logger-0.2.6.tgz", "integrity": "sha1-6302UwenLPl0zGzadraDVK0za9g=", "dev": true, "requires": {"fast-json-stable-stringify": "2.x"}}, "bser": {"version": "2.1.1", "resolved": "http://*************:4873/bser/-/bser-2.1.1.tgz", "integrity": "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=", "dev": true, "requires": {"node-int64": "^0.4.0"}}, "buffer": {"version": "5.7.1", "resolved": "http://*************:4873/buffer/-/buffer-5.7.1.tgz", "integrity": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==", "dev": true, "requires": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "buffer-from": {"version": "1.1.2", "resolved": "http://*************:4873/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=", "dev": true}, "cacache": {"version": "15.3.0", "resolved": "http://*************:4873/cacache/-/cacache-15.3.0.tgz", "integrity": "sha1-3IU4D7L1Vv492kxxm/oOyHWn8es=", "dev": true, "optional": true, "requires": {"@npmcli/fs": "^1.0.0", "@npmcli/move-file": "^1.0.1", "chownr": "^2.0.0", "fs-minipass": "^2.0.0", "glob": "^7.1.4", "infer-owner": "^1.0.4", "lru-cache": "^6.0.0", "minipass": "^3.1.1", "minipass-collect": "^1.0.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.2", "mkdirp": "^1.0.3", "p-map": "^4.0.0", "promise-inflight": "^1.0.1", "rimraf": "^3.0.2", "ssri": "^8.0.1", "tar": "^6.0.2", "unique-filename": "^1.1.1"}}, "callsites": {"version": "3.1.0", "resolved": "http://*************:4873/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true}, "camelcase": {"version": "5.3.1", "resolved": "http://*************:4873/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true}, "caniuse-lite": {"version": "1.0.30001515", "resolved": "http://*************:4873/caniuse-lite/-/caniuse-lite-1.0.30001515.tgz", "integrity": "sha512-eEFDwUOZbE24sb+Ecsx3+OvNETqjWIdabMy52oOkIgcUtAsQifjUG9q4U9dgTHJM2mfk4uEPxc0+xuFdJ629QA==", "dev": true}, "chalk": {"version": "4.1.2", "resolved": "http://*************:4873/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "char-regex": {"version": "1.0.2", "resolved": "http://*************:4873/char-regex/-/char-regex-1.0.2.tgz", "integrity": "sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=", "dev": true}, "chownr": {"version": "2.0.0", "resolved": "http://*************:4873/chownr/-/chownr-2.0.0.tgz", "integrity": "sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=", "dev": true}, "ci-info": {"version": "3.8.0", "resolved": "http://*************:4873/ci-info/-/ci-info-3.8.0.tgz", "integrity": "sha512-eXTggHWSooYhq49F2opQhuHWgzucfF2YgODK4e1566GQs5BIfP30B0oenwBJHfWxAs2fyPB1s7Mg949zLf61Yw==", "dev": true}, "cjs-module-lexer": {"version": "1.2.3", "resolved": "http://*************:4873/cjs-module-lexer/-/cjs-module-lexer-1.2.3.tgz", "integrity": "sha512-0TNiGstbQmCFwt4akjjBg5pLRTSyj/PkWQ1ZoO2zntmg9yLqSRxwEa4iCfQLGjqhiqBfOJa7W/E8wfGrTDmlZQ==", "dev": true}, "clean-stack": {"version": "2.2.0", "resolved": "http://*************:4873/clean-stack/-/clean-stack-2.2.0.tgz", "integrity": "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=", "dev": true, "optional": true}, "cliui": {"version": "8.0.1", "resolved": "http://*************:4873/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "dev": true, "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}}, "co": {"version": "4.6.0", "resolved": "http://*************:4873/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "dev": true}, "collect-v8-coverage": {"version": "1.0.2", "resolved": "http://*************:4873/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "integrity": "sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==", "dev": true}, "color-convert": {"version": "2.0.1", "resolved": "http://*************:4873/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://*************:4873/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "color-support": {"version": "1.1.3", "resolved": "http://*************:4873/color-support/-/color-support-1.1.3.tgz", "integrity": "sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI=", "dev": true, "optional": true}, "concat-map": {"version": "0.0.1", "resolved": "http://*************:4873/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "console-control-strings": {"version": "1.1.0", "resolved": "http://*************:4873/console-control-strings/-/console-control-strings-1.1.0.tgz", "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=", "dev": true, "optional": true}, "convert-source-map": {"version": "2.0.0", "resolved": "http://*************:4873/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true}, "cookie": {"version": "0.5.0", "resolved": "http://*************:4873/cookie/-/cookie-0.5.0.tgz", "integrity": "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw=="}, "cross-spawn": {"version": "7.0.3", "resolved": "http://*************:4873/cross-spawn/-/cross-spawn-7.0.3.tgz", "integrity": "sha1-9zqFudXUHQRVUcF34ogtSshXKKY=", "dev": true, "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "dayjs": {"version": "1.11.13", "resolved": "http://*************:4873/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==", "dev": true}, "debug": {"version": "4.3.4", "resolved": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "requires": {"ms": "2.1.2"}}, "decompress-response": {"version": "6.0.0", "resolved": "http://*************:4873/decompress-response/-/decompress-response-6.0.0.tgz", "integrity": "sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==", "dev": true, "requires": {"mimic-response": "^3.1.0"}}, "dedent": {"version": "0.7.0", "resolved": "http://*************:4873/dedent/-/dedent-0.7.0.tgz", "integrity": "sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=", "dev": true}, "deep-extend": {"version": "0.6.0", "resolved": "http://*************:4873/deep-extend/-/deep-extend-0.6.0.tgz", "integrity": "sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=", "dev": true}, "deepmerge": {"version": "4.3.1", "resolved": "http://*************:4873/deepmerge/-/deepmerge-4.3.1.tgz", "integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==", "dev": true}, "delegates": {"version": "1.0.0", "resolved": "http://*************:4873/delegates/-/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=", "dev": true, "optional": true}, "depd": {"version": "2.0.0", "resolved": "http://*************:4873/depd/-/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "dev": true, "optional": true}, "detect-libc": {"version": "2.0.3", "resolved": "http://*************:4873/detect-libc/-/detect-libc-2.0.3.tgz", "integrity": "sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==", "dev": true}, "detect-newline": {"version": "3.1.0", "resolved": "http://*************:4873/detect-newline/-/detect-newline-3.1.0.tgz", "integrity": "sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=", "dev": true}, "diff-sequences": {"version": "29.4.3", "resolved": "http://*************:4873/diff-sequences/-/diff-sequences-29.4.3.tgz", "integrity": "sha512-ofrBgwpPhCD85kMKtE9RYFFq6OC1A89oW2vvgWZNCwxrUpRUILopY7lsYyMDSjc8g6U6aiO0Qubg6r4Wgt5ZnA==", "dev": true}, "dottie": {"version": "2.0.6", "resolved": "http://*************:4873/dottie/-/dottie-2.0.6.tgz", "integrity": "sha512-iGCHkfUc5kFekGiqhe8B/mdaurD+lakO9txNnTvKtA6PISrw86LgqHvRzWYPyoE2Ph5aMIrCw9/uko6XHTKCwA==", "dev": true}, "electron-to-chromium": {"version": "1.4.457", "resolved": "http://*************:4873/electron-to-chromium/-/electron-to-chromium-1.4.457.tgz", "integrity": "sha512-/g3UyNDmDd6ebeWapmAoiyy+Sy2HyJ+/X8KyvNeHfKRFfHaA2W8oF5fxD5F3tjBDcjpwo0iek6YNgxNXDBoEtA==", "dev": true}, "emittery": {"version": "0.13.1", "resolved": "http://*************:4873/emittery/-/emittery-0.13.1.tgz", "integrity": "sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==", "dev": true}, "emoji-regex": {"version": "8.0.0", "resolved": "http://*************:4873/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true}, "encoding": {"version": "0.1.13", "resolved": "http://*************:4873/encoding/-/encoding-0.1.13.tgz", "integrity": "sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=", "dev": true, "optional": true, "requires": {"iconv-lite": "^0.6.2"}}, "end-of-stream": {"version": "1.4.4", "resolved": "http://*************:4873/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "dev": true, "requires": {"once": "^1.4.0"}}, "env-paths": {"version": "2.2.1", "resolved": "http://*************:4873/env-paths/-/env-paths-2.2.1.tgz", "integrity": "sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=", "dev": true, "optional": true}, "err-code": {"version": "2.0.3", "resolved": "http://*************:4873/err-code/-/err-code-2.0.3.tgz", "integrity": "sha1-I8Lzt1b/38YI0w4nyalBAkgH5/k=", "dev": true, "optional": true}, "error-ex": {"version": "1.3.2", "resolved": "http://*************:4873/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "escalade": {"version": "3.1.1", "resolved": "http://*************:4873/escalade/-/escalade-3.1.1.tgz", "integrity": "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=", "dev": true}, "escape-string-regexp": {"version": "2.0.0", "resolved": "http://*************:4873/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "integrity": "sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=", "dev": true}, "esprima": {"version": "4.0.1", "resolved": "http://*************:4873/esprima/-/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "dev": true}, "execa": {"version": "5.1.1", "resolved": "http://*************:4873/execa/-/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "dev": true, "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}}, "exit": {"version": "0.1.2", "resolved": "http://*************:4873/exit/-/exit-0.1.2.tgz", "integrity": "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=", "dev": true}, "expand-template": {"version": "2.0.3", "resolved": "http://*************:4873/expand-template/-/expand-template-2.0.3.tgz", "integrity": "sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==", "dev": true}, "expect": {"version": "29.6.1", "resolved": "http://*************:4873/expect/-/expect-29.6.1.tgz", "integrity": "sha512-XEdDLonERCU1n9uR56/Stx9OqojaLAQtZf9PrCHH9Hl8YXiEIka3H4NXJ3NOIBmQJTg7+j7buh34PMHfJujc8g==", "dev": true, "requires": {"@jest/expect-utils": "^29.6.1", "@types/node": "*", "jest-get-type": "^29.4.3", "jest-matcher-utils": "^29.6.1", "jest-message-util": "^29.6.1", "jest-util": "^29.6.1"}}, "fast-glob": {"version": "3.3.2", "resolved": "http://*************:4873/fast-glob/-/fast-glob-3.3.2.tgz", "integrity": "sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==", "dev": true, "requires": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://*************:4873/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true}, "fastq": {"version": "1.17.1", "resolved": "http://*************:4873/fastq/-/fastq-1.17.1.tgz", "integrity": "sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==", "dev": true, "requires": {"reusify": "^1.0.4"}}, "fb-watchman": {"version": "2.0.2", "resolved": "http://*************:4873/fb-watchman/-/fb-watchman-2.0.2.tgz", "integrity": "sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==", "dev": true, "requires": {"bser": "2.1.1"}}, "file-uri-to-path": {"version": "1.0.0", "resolved": "http://*************:4873/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "integrity": "sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==", "dev": true}, "fill-range": {"version": "7.0.1", "resolved": "http://*************:4873/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "find-up": {"version": "4.1.0", "resolved": "http://*************:4873/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "fs-constants": {"version": "1.0.0", "resolved": "http://*************:4873/fs-constants/-/fs-constants-1.0.0.tgz", "integrity": "sha1-a+Dem+mYzhavivwkSXue6bfM2a0=", "dev": true}, "fs-minipass": {"version": "2.1.0", "resolved": "http://*************:4873/fs-minipass/-/fs-minipass-2.1.0.tgz", "integrity": "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=", "dev": true, "requires": {"minipass": "^3.0.0"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://*************:4873/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "function-bind": {"version": "1.1.2", "resolved": "http://*************:4873/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "dev": true}, "gensync": {"version": "1.0.0-beta.2", "resolved": "http://*************:4873/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true}, "get-caller-file": {"version": "2.0.5", "resolved": "http://*************:4873/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true}, "get-package-type": {"version": "0.1.0", "resolved": "http://*************:4873/get-package-type/-/get-package-type-0.1.0.tgz", "integrity": "sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=", "dev": true}, "get-stream": {"version": "6.0.1", "resolved": "http://*************:4873/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=", "dev": true}, "github-from-package": {"version": "0.0.0", "resolved": "http://*************:4873/github-from-package/-/github-from-package-0.0.0.tgz", "integrity": "sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4=", "dev": true}, "glob": {"version": "7.2.3", "resolved": "http://*************:4873/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.2", "resolved": "http://*************:4873/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "requires": {"is-glob": "^4.0.1"}}, "globals": {"version": "11.12.0", "resolved": "http://*************:4873/globals/-/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true}, "graceful-fs": {"version": "4.2.11", "resolved": "http://*************:4873/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true}, "has": {"version": "1.0.3", "resolved": "http://*************:4873/has/-/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "4.0.0", "resolved": "http://*************:4873/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "has-unicode": {"version": "2.0.1", "resolved": "http://*************:4873/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=", "dev": true, "optional": true}, "html-escaper": {"version": "2.0.2", "resolved": "http://*************:4873/html-escaper/-/html-escaper-2.0.2.tgz", "integrity": "sha1-39YAJ9o2o238viNiYsAKWCJoFFM=", "dev": true}, "http-cache-semantics": {"version": "4.1.1", "resolved": "http://*************:4873/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz", "integrity": "sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==", "dev": true, "optional": true}, "http-proxy-agent": {"version": "4.0.1", "resolved": "http://*************:4873/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "integrity": "sha1-ioyO9/WTLM+VPClsqCkblap0qjo=", "dev": true, "optional": true, "requires": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}}, "https-proxy-agent": {"version": "5.0.1", "resolved": "http://*************:4873/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "integrity": "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==", "dev": true, "optional": true, "requires": {"agent-base": "6", "debug": "4"}}, "human-signals": {"version": "2.1.0", "resolved": "http://*************:4873/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=", "dev": true}, "humanize-ms": {"version": "1.2.1", "resolved": "http://*************:4873/humanize-ms/-/humanize-ms-1.2.1.tgz", "integrity": "sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=", "dev": true, "optional": true, "requires": {"ms": "^2.0.0"}}, "iconv-lite": {"version": "0.6.3", "resolved": "http://*************:4873/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "dev": true, "optional": true, "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}, "ieee754": {"version": "1.2.1", "resolved": "http://*************:4873/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "dev": true}, "import-local": {"version": "3.1.0", "resolved": "http://*************:4873/import-local/-/import-local-3.1.0.tgz", "integrity": "sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==", "dev": true, "requires": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}}, "imurmurhash": {"version": "0.1.4", "resolved": "http://*************:4873/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "indent-string": {"version": "4.0.0", "resolved": "http://*************:4873/indent-string/-/indent-string-4.0.0.tgz", "integrity": "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=", "dev": true, "optional": true}, "infer-owner": {"version": "1.0.4", "resolved": "http://*************:4873/infer-owner/-/infer-owner-1.0.4.tgz", "integrity": "sha1-xM78qo5RBRwqQLos6KPScpWvlGc=", "dev": true, "optional": true}, "inflection": {"version": "1.13.4", "resolved": "https://registry.npmmirror.com/inflection/-/inflection-1.13.4.tgz", "integrity": "sha512-6I/HUDeYFfuNCVS3td055BaXBwKYuzw7K3ExVMStBowKo9oOAMJIXIHvdyR3iboTCp1b+1i5DSkIZTcwIktuDw==", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "http://*************:4873/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://*************:4873/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "dev": true}, "ini": {"version": "1.3.8", "resolved": "http://*************:4873/ini/-/ini-1.3.8.tgz", "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==", "dev": true}, "ip": {"version": "2.0.0", "resolved": "http://*************:4873/ip/-/ip-2.0.0.tgz", "integrity": "sha512-WKa+XuLG1A1R0UWhl2+1XQSi+fZWMsYKffMZTTYsiZaUD8k2yDAj5atimTUD2TZkyCkNEeYE5NhFZmupOGtjYQ==", "dev": true, "optional": true}, "is-arrayish": {"version": "0.2.1", "resolved": "http://*************:4873/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "is-core-module": {"version": "2.12.1", "resolved": "http://*************:4873/is-core-module/-/is-core-module-2.12.1.tgz", "integrity": "sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg==", "dev": true, "requires": {"has": "^1.0.3"}}, "is-extglob": {"version": "2.1.1", "resolved": "http://*************:4873/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://*************:4873/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true}, "is-generator-fn": {"version": "2.1.0", "resolved": "http://*************:4873/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "integrity": "sha1-fRQK3DiarzARqPKipM+m+q3/sRg=", "dev": true}, "is-glob": {"version": "4.0.3", "resolved": "http://*************:4873/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-json": {"version": "2.0.1", "resolved": "http://*************:4873/is-json/-/is-json-2.0.1.tgz", "integrity": "sha1-a+Fm0USCihMdaGiRuYPfYsOUkf8="}, "is-lambda": {"version": "1.0.1", "resolved": "http://*************:4873/is-lambda/-/is-lambda-1.0.1.tgz", "integrity": "sha1-PZh3iZ5qU+/AFgUEzeFfgubwYdU=", "dev": true, "optional": true}, "is-number": {"version": "7.0.0", "resolved": "http://*************:4873/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true}, "is-stream": {"version": "2.0.1", "resolved": "http://*************:4873/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "dev": true}, "isexe": {"version": "2.0.0", "resolved": "http://*************:4873/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "istanbul-lib-coverage": {"version": "3.2.0", "resolved": "http://*************:4873/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz", "integrity": "sha1-GJ55CdCjn6Wj361bA/cZR3cBkdM=", "dev": true}, "istanbul-lib-instrument": {"version": "5.2.1", "resolved": "http://*************:4873/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "integrity": "sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==", "dev": true, "requires": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "dependencies": {"semver": {"version": "6.3.1", "resolved": "http://*************:4873/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true}}}, "istanbul-lib-report": {"version": "3.0.0", "resolved": "http://*************:4873/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "integrity": "sha1-dRj+UupE3jcvRgp2tezan/tz2KY=", "dev": true, "requires": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^3.0.0", "supports-color": "^7.1.0"}}, "istanbul-lib-source-maps": {"version": "4.0.1", "resolved": "http://*************:4873/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "integrity": "sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=", "dev": true, "requires": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}}, "istanbul-reports": {"version": "3.1.5", "resolved": "http://*************:4873/istanbul-reports/-/istanbul-reports-3.1.5.tgz", "integrity": "sha512-nUsEMa9pBt/NOHqbcbeJEgqIlY/K7rVWUX6Lql2orY5e9roQOthbR3vtY4zzf2orPELg80fnxxk9zUyPlgwD1w==", "dev": true, "requires": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}}, "jest": {"version": "29.6.1", "resolved": "http://*************:4873/jest/-/jest-29.6.1.tgz", "integrity": "sha512-Nirw5B4nn69rVUZtemCQhwxOBhm0nsp3hmtF4rzCeWD7BkjAXRIji7xWQfnTNbz9g0aVsBX6aZK3n+23LM6uDw==", "dev": true, "requires": {"@jest/core": "^29.6.1", "@jest/types": "^29.6.1", "import-local": "^3.0.2", "jest-cli": "^29.6.1"}}, "jest-changed-files": {"version": "29.5.0", "resolved": "http://*************:4873/jest-changed-files/-/jest-changed-files-29.5.0.tgz", "integrity": "sha512-IFG34IUMUaNBIxjQXF/iu7g6EcdMrGRRxaUSw92I/2g2YC6vCdTltl4nHvt7Ci5nSJwXIkCu8Ka1DKF+X7Z1Ag==", "dev": true, "requires": {"execa": "^5.0.0", "p-limit": "^3.1.0"}}, "jest-circus": {"version": "29.6.1", "resolved": "http://*************:4873/jest-circus/-/jest-circus-29.6.1.tgz", "integrity": "sha512-tPbYLEiBU4MYAL2XoZme/bgfUeotpDBd81lgHLCbDZZFaGmECk0b+/xejPFtmiBP87GgP/y4jplcRpbH+fgCzQ==", "dev": true, "requires": {"@jest/environment": "^29.6.1", "@jest/expect": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^0.7.0", "is-generator-fn": "^2.0.0", "jest-each": "^29.6.1", "jest-matcher-utils": "^29.6.1", "jest-message-util": "^29.6.1", "jest-runtime": "^29.6.1", "jest-snapshot": "^29.6.1", "jest-util": "^29.6.1", "p-limit": "^3.1.0", "pretty-format": "^29.6.1", "pure-rand": "^6.0.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}}, "jest-cli": {"version": "29.6.1", "resolved": "http://*************:4873/jest-cli/-/jest-cli-29.6.1.tgz", "integrity": "sha512-607dSgTA4ODIN6go9w6xY3EYkyPFGicx51a69H7yfvt7lN53xNswEVLovq+E77VsTRi5fWprLH0yl4DJgE8Ing==", "dev": true, "requires": {"@jest/core": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/types": "^29.6.1", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "import-local": "^3.0.2", "jest-config": "^29.6.1", "jest-util": "^29.6.1", "jest-validate": "^29.6.1", "prompts": "^2.0.1", "yargs": "^17.3.1"}}, "jest-config": {"version": "29.6.1", "resolved": "http://*************:4873/jest-config/-/jest-config-29.6.1.tgz", "integrity": "sha512-XdjYV2fy2xYixUiV2Wc54t3Z4oxYPAELUzWnV6+mcbq0rh742X2p52pii5A3oeRzYjLnQxCsZmp0qpI6klE2cQ==", "dev": true, "requires": {"@babel/core": "^7.11.6", "@jest/test-sequencer": "^29.6.1", "@jest/types": "^29.6.1", "babel-jest": "^29.6.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-circus": "^29.6.1", "jest-environment-node": "^29.6.1", "jest-get-type": "^29.4.3", "jest-regex-util": "^29.4.3", "jest-resolve": "^29.6.1", "jest-runner": "^29.6.1", "jest-util": "^29.6.1", "jest-validate": "^29.6.1", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^29.6.1", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}}, "jest-diff": {"version": "29.6.1", "resolved": "http://*************:4873/jest-diff/-/jest-diff-29.6.1.tgz", "integrity": "sha512-FsNCvinvl8oVxpNLttNQX7FAq7vR+gMDGj90tiP7siWw1UdakWUGqrylpsYrpvj908IYckm5Y0Q7azNAozU1Kg==", "dev": true, "requires": {"chalk": "^4.0.0", "diff-sequences": "^29.4.3", "jest-get-type": "^29.4.3", "pretty-format": "^29.6.1"}}, "jest-docblock": {"version": "29.4.3", "resolved": "http://*************:4873/jest-docblock/-/jest-docblock-29.4.3.tgz", "integrity": "sha512-fzdTftThczeSD9nZ3fzA/4KkHtnmllawWrXO69vtI+L9WjEIuXWs4AmyME7lN5hU7dB0sHhuPfcKofRsUb/2Fg==", "dev": true, "requires": {"detect-newline": "^3.0.0"}}, "jest-each": {"version": "29.6.1", "resolved": "http://*************:4873/jest-each/-/jest-each-29.6.1.tgz", "integrity": "sha512-n5eoj5eiTHpKQCAVcNTT7DRqeUmJ01hsAL0Q1SMiBHcBcvTKDELixQOGMCpqhbIuTcfC4kMfSnpmDqRgRJcLNQ==", "dev": true, "requires": {"@jest/types": "^29.6.1", "chalk": "^4.0.0", "jest-get-type": "^29.4.3", "jest-util": "^29.6.1", "pretty-format": "^29.6.1"}}, "jest-environment-node": {"version": "29.6.1", "resolved": "http://*************:4873/jest-environment-node/-/jest-environment-node-29.6.1.tgz", "integrity": "sha512-ZNIfAiE+foBog24W+2caIldl4Irh8Lx1PUhg/GZ0odM1d/h2qORAsejiFc7zb+SEmYPn1yDZzEDSU5PmDkmVLQ==", "dev": true, "requires": {"@jest/environment": "^29.6.1", "@jest/fake-timers": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "jest-mock": "^29.6.1", "jest-util": "^29.6.1"}}, "jest-get-type": {"version": "29.4.3", "resolved": "http://*************:4873/jest-get-type/-/jest-get-type-29.4.3.tgz", "integrity": "sha512-J5Xez4nRRMjk8emnTpWrlkyb9pfRQQanDrvWHhsR1+VUfbwxi30eVcZFlcdGInRibU4G5LwHXpI7IRHU0CY+gg==", "dev": true}, "jest-haste-map": {"version": "29.6.1", "resolved": "http://*************:4873/jest-haste-map/-/jest-haste-map-29.6.1.tgz", "integrity": "sha512-0m7f9PZXxOCk1gRACiVgX85knUKPKLPg4oRCjLoqIm9brTHXaorMA0JpmtmVkQiT8nmXyIVoZd/nnH1cfC33ig==", "dev": true, "requires": {"@jest/types": "^29.6.1", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "fsevents": "^2.3.2", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.4.3", "jest-util": "^29.6.1", "jest-worker": "^29.6.1", "micromatch": "^4.0.4", "walker": "^1.0.8"}}, "jest-leak-detector": {"version": "29.6.1", "resolved": "http://*************:4873/jest-leak-detector/-/jest-leak-detector-29.6.1.tgz", "integrity": "sha512-OrxMNyZirpOEwkF3UHnIkAiZbtkBWiye+hhBweCHkVbCgyEy71Mwbb5zgeTNYWJBi1qgDVfPC1IwO9dVEeTLwQ==", "dev": true, "requires": {"jest-get-type": "^29.4.3", "pretty-format": "^29.6.1"}}, "jest-matcher-utils": {"version": "29.6.1", "resolved": "http://*************:4873/jest-matcher-utils/-/jest-matcher-utils-29.6.1.tgz", "integrity": "sha512-SLaztw9d2mfQQKHmJXKM0HCbl2PPVld/t9Xa6P9sgiExijviSp7TnZZpw2Fpt+OI3nwUO/slJbOfzfUMKKC5QA==", "dev": true, "requires": {"chalk": "^4.0.0", "jest-diff": "^29.6.1", "jest-get-type": "^29.4.3", "pretty-format": "^29.6.1"}}, "jest-message-util": {"version": "29.6.1", "resolved": "http://*************:4873/jest-message-util/-/jest-message-util-29.6.1.tgz", "integrity": "sha512-KoAW2zAmNSd3Gk88uJ56qXUWbFk787QKmjjJVOjtGFmmGSZgDBrlIL4AfQw1xyMYPNVD7dNInfIbur9B2rd/wQ==", "dev": true, "requires": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.1", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.6.1", "slash": "^3.0.0", "stack-utils": "^2.0.3"}}, "jest-mock": {"version": "29.6.1", "resolved": "http://*************:4873/jest-mock/-/jest-mock-29.6.1.tgz", "integrity": "sha512-brovyV9HBkjXAEdRooaTQK42n8usKoSRR3gihzUpYeV/vwqgSoNfrksO7UfSACnPmxasO/8TmHM3w9Hp3G1dgw==", "dev": true, "requires": {"@jest/types": "^29.6.1", "@types/node": "*", "jest-util": "^29.6.1"}}, "jest-pnp-resolver": {"version": "1.2.3", "resolved": "http://*************:4873/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "integrity": "sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==", "dev": true, "requires": {}}, "jest-regex-util": {"version": "29.4.3", "resolved": "http://*************:4873/jest-regex-util/-/jest-regex-util-29.4.3.tgz", "integrity": "sha512-O4FglZaMmWXbGHSQInfXewIsd1LMn9p3ZXB/6r4FOkyhX2/iP/soMG98jGvk/A3HAN78+5VWcBGO0BJAPRh4kg==", "dev": true}, "jest-resolve": {"version": "29.6.1", "resolved": "http://*************:4873/jest-resolve/-/jest-resolve-29.6.1.tgz", "integrity": "sha512-AeRkyS8g37UyJiP9w3mmI/VXU/q8l/IH52vj/cDAyScDcemRbSBhfX/NMYIGilQgSVwsjxrCHf3XJu4f+lxCMg==", "dev": true, "requires": {"chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "jest-pnp-resolver": "^1.2.2", "jest-util": "^29.6.1", "jest-validate": "^29.6.1", "resolve": "^1.20.0", "resolve.exports": "^2.0.0", "slash": "^3.0.0"}}, "jest-resolve-dependencies": {"version": "29.6.1", "resolved": "http://*************:4873/jest-resolve-dependencies/-/jest-resolve-dependencies-29.6.1.tgz", "integrity": "sha512-BbFvxLXtcldaFOhNMXmHRWx1nXQO5LoXiKSGQcA1LxxirYceZT6ch8KTE1bK3X31TNG/JbkI7OkS/ABexVahiw==", "dev": true, "requires": {"jest-regex-util": "^29.4.3", "jest-snapshot": "^29.6.1"}}, "jest-runner": {"version": "29.6.1", "resolved": "http://*************:4873/jest-runner/-/jest-runner-29.6.1.tgz", "integrity": "sha512-tw0wb2Q9yhjAQ2w8rHRDxteryyIck7gIzQE4Reu3JuOBpGp96xWgF0nY8MDdejzrLCZKDcp8JlZrBN/EtkQvPQ==", "dev": true, "requires": {"@jest/console": "^29.6.1", "@jest/environment": "^29.6.1", "@jest/test-result": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.13.1", "graceful-fs": "^4.2.9", "jest-docblock": "^29.4.3", "jest-environment-node": "^29.6.1", "jest-haste-map": "^29.6.1", "jest-leak-detector": "^29.6.1", "jest-message-util": "^29.6.1", "jest-resolve": "^29.6.1", "jest-runtime": "^29.6.1", "jest-util": "^29.6.1", "jest-watcher": "^29.6.1", "jest-worker": "^29.6.1", "p-limit": "^3.1.0", "source-map-support": "0.5.13"}}, "jest-runtime": {"version": "29.6.1", "resolved": "http://*************:4873/jest-runtime/-/jest-runtime-29.6.1.tgz", "integrity": "sha512-D6/AYOA+Lhs5e5il8+5pSLemjtJezUr+8zx+Sn8xlmOux3XOqx4d8l/2udBea8CRPqqrzhsKUsN/gBDE/IcaPQ==", "dev": true, "requires": {"@jest/environment": "^29.6.1", "@jest/fake-timers": "^29.6.1", "@jest/globals": "^29.6.1", "@jest/source-map": "^29.6.0", "@jest/test-result": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "cjs-module-lexer": "^1.0.0", "collect-v8-coverage": "^1.0.0", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "jest-message-util": "^29.6.1", "jest-mock": "^29.6.1", "jest-regex-util": "^29.4.3", "jest-resolve": "^29.6.1", "jest-snapshot": "^29.6.1", "jest-util": "^29.6.1", "slash": "^3.0.0", "strip-bom": "^4.0.0"}}, "jest-snapshot": {"version": "29.6.1", "resolved": "http://*************:4873/jest-snapshot/-/jest-snapshot-29.6.1.tgz", "integrity": "sha512-G4UQE1QQ6OaCgfY+A0uR1W2AY0tGXUPQpoUClhWHq1Xdnx1H6JOrC2nH5lqnOEqaDgbHFgIwZ7bNq24HpB180A==", "dev": true, "requires": {"@babel/core": "^7.11.6", "@babel/generator": "^7.7.2", "@babel/plugin-syntax-jsx": "^7.7.2", "@babel/plugin-syntax-typescript": "^7.7.2", "@babel/types": "^7.3.3", "@jest/expect-utils": "^29.6.1", "@jest/transform": "^29.6.1", "@jest/types": "^29.6.1", "@types/prettier": "^2.1.5", "babel-preset-current-node-syntax": "^1.0.0", "chalk": "^4.0.0", "expect": "^29.6.1", "graceful-fs": "^4.2.9", "jest-diff": "^29.6.1", "jest-get-type": "^29.4.3", "jest-matcher-utils": "^29.6.1", "jest-message-util": "^29.6.1", "jest-util": "^29.6.1", "natural-compare": "^1.4.0", "pretty-format": "^29.6.1", "semver": "^7.5.3"}}, "jest-util": {"version": "29.6.1", "resolved": "http://*************:4873/jest-util/-/jest-util-29.6.1.tgz", "integrity": "sha512-NRFCcjc+/uO3ijUVyNOQJluf8PtGCe/W6cix36+M3cTFgiYqFOOW5MgN4JOOcvbUhcKTYVd1CvHz/LWi8d16Mg==", "dev": true, "requires": {"@jest/types": "^29.6.1", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}}, "jest-validate": {"version": "29.6.1", "resolved": "http://*************:4873/jest-validate/-/jest-validate-29.6.1.tgz", "integrity": "sha512-r3Ds69/0KCN4vx4sYAbGL1EVpZ7MSS0vLmd3gV78O+NAx3PDQQukRU5hNHPXlyqCgFY8XUk7EuTMLugh0KzahA==", "dev": true, "requires": {"@jest/types": "^29.6.1", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.4.3", "leven": "^3.1.0", "pretty-format": "^29.6.1"}, "dependencies": {"camelcase": {"version": "6.3.0", "resolved": "http://*************:4873/camelcase/-/camelcase-6.3.0.tgz", "integrity": "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==", "dev": true}}}, "jest-watcher": {"version": "29.6.1", "resolved": "http://*************:4873/jest-watcher/-/jest-watcher-29.6.1.tgz", "integrity": "sha512-d4wpjWTS7HEZPaaj8m36QiaP856JthRZkrgcIY/7ISoUWPIillrXM23WPboZVLbiwZBt4/qn2Jke84Sla6JhFA==", "dev": true, "requires": {"@jest/test-result": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.6.1", "string-length": "^4.0.1"}}, "jest-worker": {"version": "29.6.1", "resolved": "http://*************:4873/jest-worker/-/jest-worker-29.6.1.tgz", "integrity": "sha512-U+Wrbca7S8ZAxAe9L6nb6g8kPdia5hj32Puu5iOqBCMTMWFHXuK6dOV2IFrpedbTV8fjMFLdWNttQTBL6u2MRA==", "dev": true, "requires": {"@types/node": "*", "jest-util": "^29.6.1", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "dependencies": {"supports-color": {"version": "8.1.1", "resolved": "http://*************:4873/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "js-tokens": {"version": "4.0.0", "resolved": "http://*************:4873/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true}, "js-yaml": {"version": "3.14.1", "resolved": "http://*************:4873/js-yaml/-/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsesc": {"version": "2.5.2", "resolved": "http://*************:4873/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=", "dev": true}, "json-parse-even-better-errors": {"version": "2.3.1", "resolved": "http://*************:4873/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "dev": true}, "json5": {"version": "2.2.3", "resolved": "http://*************:4873/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true}, "kleur": {"version": "3.0.3", "resolved": "http://*************:4873/kleur/-/kleur-3.0.3.tgz", "integrity": "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=", "dev": true}, "leven": {"version": "3.1.0", "resolved": "http://*************:4873/leven/-/leven-3.1.0.tgz", "integrity": "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=", "dev": true}, "lines-and-columns": {"version": "1.2.4", "resolved": "http://*************:4873/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI=", "dev": true}, "locate-path": {"version": "5.0.0", "resolved": "http://*************:4873/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "lodash": {"version": "4.17.21", "resolved": "http://*************:4873/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "lodash.memoize": {"version": "4.1.2", "resolved": "http://*************:4873/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "integrity": "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=", "dev": true}, "lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "dev": true, "requires": {"yallist": "^4.0.0"}}, "lz-string": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/lz-string/-/lz-string-1.5.0.tgz", "integrity": "sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ=="}, "make-dir": {"version": "3.1.0", "resolved": "http://*************:4873/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dev": true, "requires": {"semver": "^6.0.0"}, "dependencies": {"semver": {"version": "6.3.1", "resolved": "http://*************:4873/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true}}}, "make-error": {"version": "1.3.6", "resolved": "http://*************:4873/make-error/-/make-error-1.3.6.tgz", "integrity": "sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=", "dev": true}, "make-fetch-happen": {"version": "9.1.0", "resolved": "http://*************:4873/make-fetch-happen/-/make-fetch-happen-9.1.0.tgz", "integrity": "sha1-UwhaCeeXFDPmdl95cb9j9OBcuWg=", "dev": true, "optional": true, "requires": {"agentkeepalive": "^4.1.3", "cacache": "^15.2.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "minipass": "^3.1.3", "minipass-collect": "^1.0.2", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "socks-proxy-agent": "^6.0.0", "ssri": "^8.0.0"}}, "makeerror": {"version": "1.0.12", "resolved": "http://*************:4873/makeerror/-/makeerror-1.0.12.tgz", "integrity": "sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==", "dev": true, "requires": {"tmpl": "1.0.5"}}, "merge-stream": {"version": "2.0.0", "resolved": "http://*************:4873/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=", "dev": true}, "merge2": {"version": "1.4.1", "resolved": "http://*************:4873/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true}, "micromatch": {"version": "4.0.5", "resolved": "http://*************:4873/micromatch/-/micromatch-4.0.5.tgz", "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "dev": true, "requires": {"braces": "^3.0.2", "picomatch": "^2.3.1"}}, "mimic-fn": {"version": "2.1.0", "resolved": "http://*************:4873/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "dev": true}, "mimic-response": {"version": "3.1.0", "resolved": "http://*************:4873/mimic-response/-/mimic-response-3.1.0.tgz", "integrity": "sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==", "dev": true}, "minimatch": {"version": "3.1.2", "resolved": "http://*************:4873/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.8", "resolved": "http://*************:4873/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true}, "minipass": {"version": "3.3.6", "resolved": "http://*************:4873/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "dev": true, "requires": {"yallist": "^4.0.0"}}, "minipass-collect": {"version": "1.0.2", "resolved": "http://*************:4873/minipass-collect/-/minipass-collect-1.0.2.tgz", "integrity": "sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc=", "dev": true, "optional": true, "requires": {"minipass": "^3.0.0"}}, "minipass-fetch": {"version": "1.4.1", "resolved": "http://*************:4873/minipass-fetch/-/minipass-fetch-1.4.1.tgz", "integrity": "sha1-114AkdqsGw/9fp1BYp+v99DB8bY=", "dev": true, "optional": true, "requires": {"encoding": "^0.1.12", "minipass": "^3.1.0", "minipass-sized": "^1.0.3", "minizlib": "^2.0.0"}}, "minipass-flush": {"version": "1.0.5", "resolved": "http://*************:4873/minipass-flush/-/minipass-flush-1.0.5.tgz", "integrity": "sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M=", "dev": true, "optional": true, "requires": {"minipass": "^3.0.0"}}, "minipass-pipeline": {"version": "1.2.4", "resolved": "http://*************:4873/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz", "integrity": "sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw=", "dev": true, "optional": true, "requires": {"minipass": "^3.0.0"}}, "minipass-sized": {"version": "1.0.3", "resolved": "http://*************:4873/minipass-sized/-/minipass-sized-1.0.3.tgz", "integrity": "sha1-cO5afFBSBwr6z7wil36nne81O3A=", "dev": true, "optional": true, "requires": {"minipass": "^3.0.0"}}, "minizlib": {"version": "2.1.2", "resolved": "http://*************:4873/minizlib/-/minizlib-2.1.2.tgz", "integrity": "sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=", "dev": true, "requires": {"minipass": "^3.0.0", "yallist": "^4.0.0"}}, "mkdirp": {"version": "1.0.4", "resolved": "http://*************:4873/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha1-PrXtYmInVteaXw4qIh3+utdcL34=", "dev": true}, "mkdirp-classic": {"version": "0.5.3", "resolved": "http://*************:4873/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz", "integrity": "sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==", "dev": true}, "moment": {"version": "2.29.4", "resolved": "https://registry.npmmirror.com/moment/-/moment-2.29.4.tgz", "integrity": "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==", "dev": true}, "moment-timezone": {"version": "0.5.43", "resolved": "https://registry.npmmirror.com/moment-timezone/-/moment-timezone-0.5.43.tgz", "integrity": "sha512-72j3aNyuIsDxdF1i7CEgV2FfxM1r6aaqJyLB2vwb33mXYyoyLly+F1zbWqhA3/bVIoJ4szlUoMbUnVdid32NUQ==", "dev": true, "requires": {"moment": "^2.29.4"}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true}, "nanoid": {"version": "3.3.7", "resolved": "http://*************:4873/nanoid/-/nanoid-3.3.7.tgz", "integrity": "sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g=="}, "napi-build-utils": {"version": "1.0.2", "resolved": "http://*************:4873/napi-build-utils/-/napi-build-utils-1.0.2.tgz", "integrity": "sha512-ONmRUqK7zj7DWX0D9ADe03wbwOBZxNAfF20PlGfCWQcD3+/MakShIHrMqx9YwPTfxDdF1zLeL+RGZiR9kGMLdg==", "dev": true}, "natural-compare": {"version": "1.4.0", "resolved": "http://*************:4873/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "negotiator": {"version": "0.6.3", "resolved": "http://*************:4873/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==", "dev": true, "optional": true}, "node-abi": {"version": "3.68.0", "resolved": "http://*************:4873/node-abi/-/node-abi-3.68.0.tgz", "integrity": "sha512-7vbj10trelExNjFSBm5kTvZXXa7pZyKWx9RCKIyqe6I9Ev3IzGpQoqBP3a+cOdxY+pWj6VkP28n/2wWysBHD/A==", "dev": true, "requires": {"semver": "^7.3.5"}}, "node-addon-api": {"version": "7.1.1", "resolved": "http://*************:4873/node-addon-api/-/node-addon-api-7.1.1.tgz", "integrity": "sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==", "dev": true}, "node-gyp": {"version": "8.4.1", "resolved": "http://*************:4873/node-gyp/-/node-gyp-8.4.1.tgz", "integrity": "sha1-PUkwj8MfdoGAlX1rV0aEX71CmTc=", "dev": true, "optional": true, "requires": {"env-paths": "^2.2.0", "glob": "^7.1.4", "graceful-fs": "^4.2.6", "make-fetch-happen": "^9.1.0", "nopt": "^5.0.0", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.2", "which": "^2.0.2"}, "dependencies": {"are-we-there-yet": {"version": "3.0.1", "resolved": "http://*************:4873/are-we-there-yet/-/are-we-there-yet-3.0.1.tgz", "integrity": "sha512-QZW4EDmGwlYur0Yyf/b2uGucHQMa8aFUP7eu9ddR73vvhFyt4V0Vl3QHPcTNJ8l6qYOBdxgXdnBXQrHilfRQBg==", "dev": true, "optional": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}}, "gauge": {"version": "4.0.4", "resolved": "http://*************:4873/gauge/-/gauge-4.0.4.tgz", "integrity": "sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg==", "dev": true, "optional": true, "requires": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.3", "console-control-strings": "^1.1.0", "has-unicode": "^2.0.1", "signal-exit": "^3.0.7", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5"}}, "npmlog": {"version": "6.0.2", "resolved": "http://*************:4873/npmlog/-/npmlog-6.0.2.tgz", "integrity": "sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==", "dev": true, "optional": true, "requires": {"are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0", "gauge": "^4.0.3", "set-blocking": "^2.0.0"}}}}, "node-int64": {"version": "0.4.0", "resolved": "http://*************:4873/node-int64/-/node-int64-0.4.0.tgz", "integrity": "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=", "dev": true}, "node-releases": {"version": "2.0.13", "resolved": "http://*************:4873/node-releases/-/node-releases-2.0.13.tgz", "integrity": "sha512-uYr7J37ae/ORWdZeQ1xxMJe3NtdmqMC/JZK+geofDrkLUApKRHPd18/TxtBOJ4A0/+uUIliorNrfYV6s1b02eQ==", "dev": true}, "nopt": {"version": "5.0.0", "resolved": "http://*************:4873/nopt/-/nopt-5.0.0.tgz", "integrity": "sha1-UwlCu1ilEvzK/lP+IQ8TolNV3Ig=", "dev": true, "optional": true, "requires": {"abbrev": "1"}}, "normalize-path": {"version": "3.0.0", "resolved": "http://*************:4873/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true}, "npm-run-path": {"version": "4.0.1", "resolved": "http://*************:4873/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "dev": true, "requires": {"path-key": "^3.0.0"}}, "once": {"version": "1.4.0", "resolved": "http://*************:4873/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.2", "resolved": "http://*************:4873/onetime/-/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "dev": true, "requires": {"mimic-fn": "^2.1.0"}}, "p-limit": {"version": "3.1.0", "resolved": "http://*************:4873/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "dev": true, "requires": {"yocto-queue": "^0.1.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://*************:4873/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}, "dependencies": {"p-limit": {"version": "2.3.0", "resolved": "http://*************:4873/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "requires": {"p-try": "^2.0.0"}}}}, "p-map": {"version": "4.0.0", "resolved": "http://*************:4873/p-map/-/p-map-4.0.0.tgz", "integrity": "sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=", "dev": true, "optional": true, "requires": {"aggregate-error": "^3.0.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://*************:4873/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "parse-json": {"version": "5.2.0", "resolved": "http://*************:4873/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}}, "path-exists": {"version": "4.0.0", "resolved": "http://*************:4873/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://*************:4873/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true}, "path-key": {"version": "3.1.1", "resolved": "http://*************:4873/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true}, "path-parse": {"version": "1.0.7", "resolved": "http://*************:4873/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true}, "pg-connection-string": {"version": "2.6.0", "resolved": "https://registry.npmmirror.com/pg-connection-string/-/pg-connection-string-2.6.0.tgz", "integrity": "sha512-x14ibktcwlHKoHxx9X3uTVW9zIGR41ZB6QNhHb21OPNdCCO3NaRnpJuwKIQSR4u+Yqjx4HCvy7Hh7VSy1U4dGg==", "dev": true}, "picocolors": {"version": "1.0.0", "resolved": "http://*************:4873/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=", "dev": true}, "picomatch": {"version": "2.3.1", "resolved": "http://*************:4873/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true}, "pirates": {"version": "4.0.6", "resolved": "http://*************:4873/pirates/-/pirates-4.0.6.tgz", "integrity": "sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==", "dev": true}, "pkg-dir": {"version": "4.2.0", "resolved": "http://*************:4873/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "requires": {"find-up": "^4.0.0"}}, "prebuild-install": {"version": "7.1.2", "resolved": "http://*************:4873/prebuild-install/-/prebuild-install-7.1.2.tgz", "integrity": "sha512-UnNke3IQb6sgarcZIDU3gbMeTp/9SSU1DAIkil7PrqG1vZlBtY5msYccSKSHDqa3hNg436IXK+SNImReuA1wEQ==", "dev": true, "requires": {"detect-libc": "^2.0.0", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.3", "mkdirp-classic": "^0.5.3", "napi-build-utils": "^1.0.1", "node-abi": "^3.3.0", "pump": "^3.0.0", "rc": "^1.2.7", "simple-get": "^4.0.0", "tar-fs": "^2.0.0", "tunnel-agent": "^0.6.0"}}, "pretty-format": {"version": "29.6.1", "resolved": "http://*************:4873/pretty-format/-/pretty-format-29.6.1.tgz", "integrity": "sha512-7jRj+yXO0W7e4/tSJKoR7HRIHLPPjtNaUGG2xxKQnGvPNRkgWcQ0AZX6P4KBRJN4FcTBWb3sa7DVUJmocYuoog==", "dev": true, "requires": {"@jest/schemas": "^29.6.0", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "dependencies": {"ansi-styles": {"version": "5.2.0", "resolved": "http://*************:4873/ansi-styles/-/ansi-styles-5.2.0.tgz", "integrity": "sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=", "dev": true}}}, "promise-inflight": {"version": "1.0.1", "resolved": "http://*************:4873/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "dev": true, "optional": true}, "promise-retry": {"version": "2.0.1", "resolved": "http://*************:4873/promise-retry/-/promise-retry-2.0.1.tgz", "integrity": "sha1-/3R6E2IKtXumiPX8Z4VUEMNw2iI=", "dev": true, "optional": true, "requires": {"err-code": "^2.0.2", "retry": "^0.12.0"}}, "prompts": {"version": "2.4.2", "resolved": "http://*************:4873/prompts/-/prompts-2.4.2.tgz", "integrity": "sha1-e1fnOzpIAprRDr1E90sBcipMsGk=", "dev": true, "requires": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}}, "pump": {"version": "3.0.2", "resolved": "http://*************:4873/pump/-/pump-3.0.2.tgz", "integrity": "sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pure-rand": {"version": "6.0.2", "resolved": "http://*************:4873/pure-rand/-/pure-rand-6.0.2.tgz", "integrity": "sha512-6Yg0ekpKICSjPswYOuC5sku/TSWaRYlA0qsXqJgM/d/4pLPHPuTxK7Nbf7jFKzAeedUhR8C7K9Uv63FBsSo8xQ==", "dev": true}, "queue-microtask": {"version": "1.2.3", "resolved": "http://*************:4873/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true}, "rc": {"version": "1.2.8", "resolved": "http://*************:4873/rc/-/rc-1.2.8.tgz", "integrity": "sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=", "dev": true, "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"strip-json-comments": {"version": "2.0.1", "resolved": "http://*************:4873/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==", "dev": true}}}, "react-is": {"version": "18.2.0", "resolved": "http://*************:4873/react-is/-/react-is-18.2.0.tgz", "integrity": "sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==", "dev": true}, "readable-stream": {"version": "3.6.2", "resolved": "http://*************:4873/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "regenerator-runtime": {"version": "0.14.1", "resolved": "http://*************:4873/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==", "dev": true}, "require-directory": {"version": "2.1.1", "resolved": "http://*************:4873/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true}, "resolve": {"version": "1.22.3", "resolved": "http://*************:4873/resolve/-/resolve-1.22.3.tgz", "integrity": "sha512-P8ur/gp/AmbEzjr729bZnLjXK5Z+4P0zhIJgBgzqRih7hL7BOukHGtSTA3ACMY467GRFz3duQsi0bDZdR7DKdw==", "dev": true, "requires": {"is-core-module": "^2.12.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-cwd": {"version": "3.0.0", "resolved": "http://*************:4873/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "integrity": "sha1-DwB18bslRHZs9zumpuKt/ryxPy0=", "dev": true, "requires": {"resolve-from": "^5.0.0"}}, "resolve-from": {"version": "5.0.0", "resolved": "http://*************:4873/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true}, "resolve.exports": {"version": "2.0.2", "resolved": "http://*************:4873/resolve.exports/-/resolve.exports-2.0.2.tgz", "integrity": "sha512-X2UW6Nw3n/aMgDVy+0rSqgHlv39WZAlZrXCdnbyEiKm17DSqHX4MmQMaST3FbeWR5FTuRcUwYAziZajji0Y7mg==", "dev": true}, "retry": {"version": "0.12.0", "resolved": "http://*************:4873/retry/-/retry-0.12.0.tgz", "integrity": "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=", "dev": true, "optional": true}, "retry-as-promised": {"version": "7.0.4", "resolved": "https://registry.npmmirror.com/retry-as-promised/-/retry-as-promised-7.0.4.tgz", "integrity": "sha512-XgmCoxKWkDofwH8WddD0w85ZfqYz+ZHlr5yo+3YUCfycWawU56T5ckWXsScsj5B8tqUcIG67DxXByo3VUgiAdA==", "dev": true}, "reusify": {"version": "1.0.4", "resolved": "http://*************:4873/reusify/-/reusify-1.0.4.tgz", "integrity": "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==", "dev": true}, "rimraf": {"version": "3.0.2", "resolved": "http://*************:4873/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "dev": true, "optional": true, "requires": {"glob": "^7.1.3"}}, "run-parallel": {"version": "1.2.0", "resolved": "http://*************:4873/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "requires": {"queue-microtask": "^1.2.2"}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://*************:4873/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true}, "safer-buffer": {"version": "2.1.2", "resolved": "http://*************:4873/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true, "optional": true}, "semver": {"version": "7.5.4", "resolved": "http://*************:4873/semver/-/semver-7.5.4.tgz", "integrity": "sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "sequelize": {"version": "6.32.1", "resolved": "http://*************:4873/sequelize/-/sequelize-6.32.1.tgz", "integrity": "sha512-3Iv0jruv57Y0YvcxQW7BE56O7DC1BojcfIrqh6my+IQwde+9u/YnuYHzK+8kmZLhLvaziRT1eWu38nh9yVwn/g==", "dev": true, "requires": {"@types/debug": "^4.1.8", "@types/validator": "^13.7.17", "debug": "^4.3.4", "dottie": "^2.0.4", "inflection": "^1.13.4", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pg-connection-string": "^2.6.0", "retry-as-promised": "^7.0.4", "semver": "^7.5.1", "sequelize-pool": "^7.1.0", "toposort-class": "^1.0.1", "uuid": "^8.3.2", "validator": "^13.9.0", "wkx": "^0.5.0"}}, "sequelize-pool": {"version": "7.1.0", "resolved": "https://registry.npmmirror.com/sequelize-pool/-/sequelize-pool-7.1.0.tgz", "integrity": "sha512-G9c0qlIWQSK29pR/5U2JF5dDQeqqHRragoyahj/Nx4KOOQ3CPPfzxnfqFPCSB7x5UgjOgnZ61nSxz+fjDpRlJg==", "dev": true}, "set-blocking": {"version": "2.0.0", "resolved": "http://*************:4873/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true, "optional": true}, "shebang-command": {"version": "2.0.0", "resolved": "http://*************:4873/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "resolved": "http://*************:4873/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true}, "signal-exit": {"version": "3.0.7", "resolved": "http://*************:4873/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "dev": true}, "simple-concat": {"version": "1.0.1", "resolved": "http://*************:4873/simple-concat/-/simple-concat-1.0.1.tgz", "integrity": "sha1-9Gl2CCujXCJj8cirXt/ibEHJVS8=", "dev": true}, "simple-get": {"version": "4.0.1", "resolved": "http://*************:4873/simple-get/-/simple-get-4.0.1.tgz", "integrity": "sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA==", "dev": true, "requires": {"decompress-response": "^6.0.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "sisteransi": {"version": "1.0.5", "resolved": "http://*************:4873/sisteransi/-/sisteransi-1.0.5.tgz", "integrity": "sha1-E01oEpd1ZDfMBcoBNw06elcQde0=", "dev": true}, "slash": {"version": "3.0.0", "resolved": "http://*************:4873/slash/-/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "dev": true}, "smart-buffer": {"version": "4.2.0", "resolved": "http://*************:4873/smart-buffer/-/smart-buffer-4.2.0.tgz", "integrity": "sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=", "dev": true, "optional": true}, "socks": {"version": "2.7.2", "resolved": "http://*************:4873/socks/-/socks-2.7.1.tgz", "integrity": "sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==", "dev": true, "optional": true, "requires": {"ip": "^2.0.0", "smart-buffer": "^4.2.0"}}, "socks-proxy-agent": {"version": "6.2.1", "resolved": "http://*************:4873/socks-proxy-agent/-/socks-proxy-agent-6.2.1.tgz", "integrity": "sha512-a6KW9G+6B3nWZ1yB8G7pJwL3ggLy1uTzKAgCb7ttblwqdz9fMGJUuTy3uFzEP48FAs9FLILlmzDlE2JJhVQaXQ==", "dev": true, "optional": true, "requires": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}}, "source-map": {"version": "0.6.1", "resolved": "http://*************:4873/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}, "source-map-support": {"version": "0.5.13", "resolved": "http://*************:4873/source-map-support/-/source-map-support-0.5.13.tgz", "integrity": "sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "sprintf-js": {"version": "1.0.3", "resolved": "http://*************:4873/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}, "sqlite3": {"version": "5.1.7", "resolved": "http://*************:4873/sqlite3/-/sqlite3-5.1.7.tgz", "integrity": "sha512-GGIyOiFaG+TUra3JIfkI/zGP8yZYLPQ0pl1bH+ODjiX57sPhrLU5sQJn1y9bDKZUFYkX1crlrPfSYt0BKKdkog==", "dev": true, "requires": {"bindings": "^1.5.0", "node-addon-api": "^7.0.0", "node-gyp": "8.x", "prebuild-install": "^7.1.1", "tar": "^6.1.11"}}, "ssri": {"version": "8.0.1", "resolved": "http://*************:4873/ssri/-/ssri-8.0.1.tgz", "integrity": "sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=", "dev": true, "optional": true, "requires": {"minipass": "^3.1.1"}}, "stack-utils": {"version": "2.0.6", "resolved": "http://*************:4873/stack-utils/-/stack-utils-2.0.6.tgz", "integrity": "sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==", "dev": true, "requires": {"escape-string-regexp": "^2.0.0"}}, "string_decoder": {"version": "1.3.0", "resolved": "http://*************:4873/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "dev": true, "requires": {"safe-buffer": "~5.2.0"}}, "string-length": {"version": "4.0.2", "resolved": "http://*************:4873/string-length/-/string-length-4.0.2.tgz", "integrity": "sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=", "dev": true, "requires": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}}, "string-width": {"version": "4.2.3", "resolved": "http://*************:4873/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "http://*************:4873/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}, "strip-bom": {"version": "4.0.0", "resolved": "http://*************:4873/strip-bom/-/strip-bom-4.0.0.tgz", "integrity": "sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=", "dev": true}, "strip-final-newline": {"version": "2.0.0", "resolved": "http://*************:4873/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=", "dev": true}, "strip-json-comments": {"version": "3.1.1", "resolved": "http://*************:4873/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://*************:4873/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://*************:4873/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true}, "tar": {"version": "6.1.15", "resolved": "http://*************:4873/tar/-/tar-6.1.15.tgz", "integrity": "sha512-/zKt9UyngnxIT/EAGYuxaMYgOIJiP81ab9ZfkILq4oNLPFX50qyYmu7jRj9qeXoxmJHjGlbH0+cm2uy1WCs10A==", "dev": true, "requires": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "dependencies": {"minipass": {"version": "5.0.0", "resolved": "http://*************:4873/minipass/-/minipass-5.0.0.tgz", "integrity": "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==", "dev": true}}}, "tar-fs": {"version": "2.1.1", "resolved": "http://*************:4873/tar-fs/-/tar-fs-2.1.1.tgz", "integrity": "sha512-V0r2Y9scmbDRLCNex/+hYzvp/zyYjvFbHPNgVTKfQvVrb6guiE/fxP+XblDNR011utopbkex2nM4dHNV6GDsng==", "dev": true, "requires": {"chownr": "^1.1.1", "mkdirp-classic": "^0.5.2", "pump": "^3.0.0", "tar-stream": "^2.1.4"}, "dependencies": {"chownr": {"version": "1.1.4", "resolved": "http://*************:4873/chownr/-/chownr-1.1.4.tgz", "integrity": "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==", "dev": true}}}, "tar-stream": {"version": "2.2.0", "resolved": "http://*************:4873/tar-stream/-/tar-stream-2.2.0.tgz", "integrity": "sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==", "dev": true, "requires": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}}, "test-exclude": {"version": "6.0.0", "resolved": "http://*************:4873/test-exclude/-/test-exclude-6.0.0.tgz", "integrity": "sha1-BKhphmHYBepvopO2y55jrARO8V4=", "dev": true, "requires": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}}, "tmpl": {"version": "1.0.5", "resolved": "http://*************:4873/tmpl/-/tmpl-1.0.5.tgz", "integrity": "sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=", "dev": true}, "to-fast-properties": {"version": "2.0.0", "resolved": "http://*************:4873/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true}, "to-regex-range": {"version": "5.0.1", "resolved": "http://*************:4873/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "requires": {"is-number": "^7.0.0"}}, "toposort-class": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/toposort-class/-/toposort-class-1.0.1.tgz", "integrity": "sha512-OsLcGGbYF3rMjPUf8oKktyvCiUxSbqMMS39m33MAjLTC1DVIH6x3WSt63/M77ihI09+Sdfk1AXvfhCEeUmC7mg==", "dev": true}, "ts-jest": {"version": "29.1.1", "resolved": "http://*************:4873/ts-jest/-/ts-jest-29.1.1.tgz", "integrity": "sha512-D6xjnnbP17cC85nliwGiL+tpoKN0StpgE0TeOjXQTU6MVCfsB4v7aW05CgQ/1OywGb0x/oy9hHFnN+sczTiRaA==", "dev": true, "requires": {"bs-logger": "0.x", "fast-json-stable-stringify": "2.x", "jest-util": "^29.0.0", "json5": "^2.2.3", "lodash.memoize": "4.x", "make-error": "1.x", "semver": "^7.5.3", "yargs-parser": "^21.0.1"}}, "tunnel-agent": {"version": "0.6.0", "resolved": "http://*************:4873/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "dev": true, "requires": {"safe-buffer": "^5.0.1"}}, "type-detect": {"version": "4.0.8", "resolved": "http://*************:4873/type-detect/-/type-detect-4.0.8.tgz", "integrity": "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=", "dev": true}, "type-fest": {"version": "0.21.3", "resolved": "http://*************:4873/type-fest/-/type-fest-0.21.3.tgz", "integrity": "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=", "dev": true}, "typescript": {"version": "5.1.6", "resolved": "http://*************:4873/typescript/-/typescript-5.1.6.tgz", "integrity": "sha512-zaWCozRZ6DLEWAWFrVDz1H6FVXzUSfTy5FUMWsQlU8Ym5JP9eO4xkTIROFCQvhQf61z6O/G6ugw3SgAnvvm+HA==", "dev": true}, "unique-filename": {"version": "1.1.1", "resolved": "http://*************:4873/unique-filename/-/unique-filename-1.1.1.tgz", "integrity": "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=", "dev": true, "optional": true, "requires": {"unique-slug": "^2.0.0"}}, "unique-slug": {"version": "2.0.2", "resolved": "http://*************:4873/unique-slug/-/unique-slug-2.0.2.tgz", "integrity": "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=", "dev": true, "optional": true, "requires": {"imurmurhash": "^0.1.4"}}, "update-browserslist-db": {"version": "1.0.11", "resolved": "http://*************:4873/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz", "integrity": "sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==", "dev": true, "requires": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}}, "util-deprecate": {"version": "1.0.2", "resolved": "http://*************:4873/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "uuid": {"version": "8.3.2", "resolved": "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "dev": true}, "v8-to-istanbul": {"version": "9.1.0", "resolved": "http://*************:4873/v8-to-istanbul/-/v8-to-istanbul-9.1.0.tgz", "integrity": "sha512-6z3GW9x8G1gd+JIIgQQQxXuiJtCXeAjp6RaPEPLv62mH3iPHPxV6W3robxtCzNErRo6ZwTmzWhsbNvjyEBKzKA==", "dev": true, "requires": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^1.6.0"}, "dependencies": {"convert-source-map": {"version": "1.9.0", "resolved": "http://*************:4873/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==", "dev": true}}}, "validator": {"version": "13.12.0", "resolved": "http://*************:4873/validator/-/validator-13.12.0.tgz", "integrity": "sha512-c1Q0mCiPlgdTVVVIJIrBuxNicYE+t/7oKeI9MWLj3fh/uq2Pxh/3eeWbVZ4OcGW1TUf53At0njHw5SMdA3tmMg==", "dev": true}, "walker": {"version": "1.0.8", "resolved": "http://*************:4873/walker/-/walker-1.0.8.tgz", "integrity": "sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==", "dev": true, "requires": {"makeerror": "1.0.12"}}, "which": {"version": "2.0.2", "resolved": "http://*************:4873/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "requires": {"isexe": "^2.0.0"}}, "wide-align": {"version": "1.1.5", "resolved": "http://*************:4873/wide-align/-/wide-align-1.1.5.tgz", "integrity": "sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==", "dev": true, "optional": true, "requires": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "wkx": {"version": "0.5.0", "resolved": "https://registry.npmmirror.com/wkx/-/wkx-0.5.0.tgz", "integrity": "sha512-Xng/d4Ichh8uN4l0FToV/258EjMGU9MGcA0HV2d9B/ZpZB3lqQm7nkOdZdm5GhKtLLhAE7PiVQwN4eN+2YJJUg==", "dev": true, "requires": {"@types/node": "*"}}, "wrap-ansi": {"version": "7.0.0", "resolved": "http://*************:4873/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "wrappy": {"version": "1.0.2", "resolved": "http://*************:4873/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "write-file-atomic": {"version": "4.0.2", "resolved": "http://*************:4873/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "integrity": "sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==", "dev": true, "requires": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}}, "y18n": {"version": "5.0.8", "resolved": "http://*************:4873/y18n/-/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "dev": true}, "yallist": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "dev": true}, "yargs": {"version": "17.7.2", "resolved": "http://*************:4873/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "dev": true, "requires": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}}, "yargs-parser": {"version": "21.1.1", "resolved": "http://*************:4873/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "dev": true}, "yocto-queue": {"version": "0.1.0", "resolved": "http://*************:4873/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "dev": true}}}