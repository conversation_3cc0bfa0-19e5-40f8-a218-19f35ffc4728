## sugo-sequelize-cloud 基于 sequelize 的云服务

### 🚀 特点

- 完备的 Typescript 支持，让你轻松借助类型智能来编写代码，提升开发体验；
- 去 controller、service、api 化，不需要关心这些，没有复杂的概念，减少心智负担；
- 只需要定义表结构，一切可在前端通过函数式调用，并且支持批量调用，一切皆为函数；
- 支持 json 字段级的查询、支持关联查询、在前端 All in Sequelize Api 等；
- 支持请求鉴权、数据加密、自动缓存等；
- 自动根据 model 生成 type.d.ts，自动生成接口文档，自动生成 restful 接口等（还在开发中）；
- 自主研发，代码可控，轻量级实现（<1k 行）；

---

前端基本使用：

```ts
// 调用查询函数
await Service.Person.findAll({ where: { title: 'hello' } })
// 执行云函数
await Service.$execute('sum', [1, 2, 3])
```

### 安装使用

npm 安装：

```bash
npm install sugo-sequelize-cloud --registry=http://*************:4873/
# 如果用 mysql 后端需要安装
npm install mysql2
```

### 开荒流程

后端：
- 第一步 - 设计表（类型，注释，结构等）；
- 第二步 - 初始化后端的 CloudServer，注册 sequelize（和 redis）；
- 第三步 - 编写云函数并注册；

前端：
- 第四步 - 生成前端模型类型（如果无自动，需要手动编写）；
- 第五步 - 初始化前端的 Service/Cloud；
- 第六步 - 调用云服务、云函数使用；

**前端引入：**

```ts
import { createCloudService } from 'sugo-sequelize-cloud/client'

// 这里的类型命名必须跟后端的 model 一致
export interface Entity {
  Tag: {
    id: string
    title: string
  }
}

export const Service = createCloudService<Entity>({
  microApp: 'sugo-abi-report-hub',
  baseUrl: '/api/cloud', // 可以包含域名 http://abc.com/api/cloud
  umiRequest, // umi-request
  entityMap: {
    Tag: 'Tag',
    // key 是前端用，value 传给后端，
    // 例如 Tag: 'TagModel'，前端用时 Cloud.Tag.findOne => 指向的 model 为 TagModel
  }
})

// 使用
Service.Tag.findAll({})
```

**后端引入：**
只有 sequelize 版本有关系，拿到 sequelize 实例即可。

```ts
import { CloudServer } from 'sugo-sequelize-cloud/server'
import { registerFunction } from '@/clouds/register'
import { InjectDataSource } from '@midwayjs/sequelize'

@Controller('/api/cloud')
export class APIController {
  @Inject()
  ctx: Context

  @InjectDataSource()
  sequelize: Sequelize

  private _cloudServer: CloudServer
  private get cloudServer() {
    if (this._cloudServer) return this._cloudServer
    this._cloudServer = new CloudServer({
      sequelize: this.sequelize, // 关键
      registerFunction,
      logging: true
    })
    return this._cloudServer
  }

  @Post('/')
  async cloud() {
    this.ctx.body = await this.cloudServer.execKoa({
      body: this.ctx.request.body || {},
      headers: this.ctx.headers,
      session: this.ctx.session,
      userId: null, // 当前登录的用户 id
    })
  }
}
```

### 本地开发

首先，在本地模块的目录下执行 npm link 命令，将其链接到全局的 npm 包管理器中。

```shell
npm link
```

然后，在主项目的目录下执行 npm link [local_module] 命令，将全局的本地模块链接到当前项目的 node_modules 目录下。

```shell
cd /path/to/project
npm link sugo-sequelize-cloud
```

> 如果引入不生效，npm 里写 "sugo-sequelize-cloud": "file:///path/to/sugo-sequelize-cloud" 也行

### 文档

- [前端查询文档](./docs/前端查询.md)
- [后端云函数](./docs/后端云函数.md)
- [应用间服务调用](./docs/应用间服务调用.md)
- [开荒流程](./docs/开荒流程.md)
- [应用间调用](./docs/应用间调用.md)
- [关联主应用表](./docs/关联主应用表.md)
- [云函数数据权限](./docs/云函数数据权限.md)
- [基于 sequelize 的云服务建设 PPT](./docs/基于sequelize的云服务建设.pptx)

支持的函数：
- findAll
- findAndCountAll
- findOne
- findOrCreate
- findByPk
- create
- bulkCreate
- update
- updateByPk
- upsert
- count
- delete
- deleteByPk

同时也对 redis 进行支持（需要后端设置 redis 实例才能成功调用）：
- get/set/del
- mget/mset
- hget/hset/hdel、hmget/hsget
等等，主要覆盖 string，list，hash，set，zset 的几个类型操作。

支持的操作符：

- $eq?: M[K] | null
- $ne?: M[K] | null
- $not?: M[K] | null | string | number
- $gt?: M[K] | Date
- $gte?: M[K] | Date
- $lt?: M[K] | Date
- $lte?: M[K] | Date
- $like?: string | null
- $in?: (M[K] | (string & {}))[]
- $notIn?: (M[K] | (string & {}))[]
- $between?: [M[K], M[K]]
- $notBetween?: [M[K], M[K]]
- $regexp?: M[K] | null
- $notRegexp?: M[K] | null

以下为 json 数组操作符
- $contains?: (string | number)[] | Record<string, any>
- $overlap?: (string | number)[]
- $contained?: (string | number)[]
- $adjacent?: (string | number)[]
- $strictLeft?: (string | number)[]
- $noExtendRight?: (string | number)[]
- $noExtendLeft?: (string | number)[]

#### 限制：

1. 前端提交的 limit 限制最大 1000。

### TODO

- 用 $like 时，JSON 字段 sequelize 会做转义，请用 $regexp 取代。

例如：

```js
// 数据库存的是 json 数组
// userId = ['uid1', 'uid2', 'uid3']
// 因为 json 字段，实际上也是字符串
Service.Post.findAll({
  userId: {
    $regexp: 'uid1|uid2' // 查询包含 uid1 或  uid2 的
    $regexp: '.*uid1.*uid2.*' // 查询同时存在 uid1 和 uid2 的
  }
})
```

#### v1.x 计划支持功能（基础）

前端：
- [✔] 支持云服务，云函数调用；
- [✖] 支持前端的类型生成；
- [✔] 支持 json 字段的查询，支持 json 字段排除（内存计算）；
- [✔] 支持关联查询；
- [✔] 支持前端的批量查询；
- [✔] 支持前端的原子层事务；
- [✖] 支持 CURD 事件钩子；
- [✔] 支持 redis；

后端：
- [✔] 支持自定义云函数封装；
- [✖] 支持云函数定时任务；
- [✔] 支持 CURD 事件钩子（直接在原生的 sequelize 上注册即可）；

#### v2.x 计划支持功能（增强）

前端：
- [✔] 支持请求鉴权与加密；

后端：
- [✔] 支持请求鉴权与加密；
- [✖] 支持内置接口监控；
- [✔] 支持查询、新增，更新权限控制；
- [✖] 支持自动根据 model 生成 restful 接口；
- [✔] 支持自动缓存功能；
- [✖] 支持自动根据 model 生成前端 Typescript 类型；
- [✔] 支持跨库查询；
- [✔] 支持自动关联用户信息；

#### v3.x 计划支持功能（平台化）

后端：
- [✖] 支持可视化建模；
- [✖] 支持在线云函数编辑；
