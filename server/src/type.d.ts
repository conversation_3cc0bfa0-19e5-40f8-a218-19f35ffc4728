// import type { Sequelize } from 'sequelize'
import { IncomingHttpHeaders } from 'http'

type Sequelize = any
type Redis = any

export type WhereType<M = any> =
  | {
    [K in keyof M]?:
    | M[K]
    | null
    | {
      $eq?: M[K] | null
      $ne?: M[K] | null
      $not?: M[K] | null | string | number
      $gt?: M[K] | Date
      $gte?: M[K] | Date
      $lt?: M[K] | Date
      $lte?: M[K] | Date
      $like?: string | null
      $in?: (M[K] | (string & {}))[]
      $notIn?: (M[K] | (string & {}))[]
      $between?: [M[K], M[K]]
      $notBetween?: [M[K], M[K]]
      $regexp?: M[K] | null
      $notRegexp?: M[K] | null
      // 以下为 json 数组操作符
      $contains?: (string | number)[] | Record<string, any>
      $overlap?: (string | number)[]
      $contained?: (string | number)[]
      $adjacent?: (string | number)[]
      $strictLeft?: (string | number)[]
      $noExtendRight?: (string | number)[]
      $noExtendLeft?: (string | number)[]
    }
    | {
      $and?: WhereType<M>[]
      $or?: WhereType<M>[]
    }
  }
  | {
    $and?: WhereType<M>[]
    $or?: WhereType<M>[]
  }
  | string
  | {
    [key: string]: any
  }

export type Relation = {
  tableName: string // 类似 SugoUser
  /** 关联字段名称，默认 id */
  relatedName?: string
  fieldMapping: {
    /** 新字段名称 */
    [key: string]: {
      /** 原字段名称 */
      sourceName: string
      /** 类似 select */
      attributes?: (string | [string, string])[] // 类似 attributes: ['id', 'username']
    }
  }
}

export type LikeAttributes<M> = (keyof M)[] | string[][] | (string | string[])[] | {
  exclude?: (keyof M)[]
  include?: (keyof M)[] | string[][]
} |
{
  [key in Exclude<keyof M, 'exclude' | 'include'>]: string
}

export type LikeInclude<M> = {
  /** 关联的模型 */
  model: string
  /** 别名，这个要跟模型的属性名称一致，不然会报错，一般不用设置 */
  as?: string
  where?: WhereType<M>
  attributes?: LikeAttributes<M>
  limit?: number
  /** 指定的关联查询是否必须，默认为 true，关联不了将不查询出来 */
  required?: boolean
  include?: LikeInclude<M>[],
  /** 设置为 false 时即可查询假删除的 */
  paranoid?: boolean
  /** 作用域 */
  scopes?: Record<string, any>
}

export interface CloudApiParams<M = any> {
  functionName: string
  modelName: string
  namespace?: '$cloud' | '$redis' | '$use' | (string & {})
  params: {
    where?: WhereType<M>
    limit?: number
    offset?: number
    /** 关联查询 include 语法 */
    include?: string | LikeInclude<any> | LikeInclude<any>[] | string[]
    /** 选择字段，或排除字段 */
    attributes?: LikeAttributes<M>
    transaction?: any
    distinct?: boolean
    order?:
    | {
      [key in keyof M]: 'DESC' | 'ASC'
    }
    | [keyof M, 'DESC' | 'ASC'][]
    | [string, 'DESC' | 'ASC'][]
    /**
     * @deprecated 警告此参数将抛弃，请用 group 字段
     */
    groupBy?: keyof M | (string & {})[]
    group?: (keyof M)[] | string[]
    having?: Record<string, any>
    paranoid?: boolean
    /** 创建后是否不返回 */
    returning?: boolean
    usePermission?: boolean | 'new' | (string & {})
    /**
     * 主应用/其他应用关联表查询
     *
     * @example
     * [{
     *   tableName: 'SugoUser',
     *   fieldMapping: {
     *     createdBy: {
     *       fieldName: 'createdUser',
     *       attributes: ['id', 'username', 'first_name']
     *     }
     *   }
     * }]
     */
    relations?: Relation | Relation[]
  }
  data?: any | any[] | undefined | null
  config?: {
    dbName?: string
    tableName?: string
    mapDbName?: string
  }
  userId?: string
}

export type FindOption<M = any> = NonNullable<CloudApiParams<M>['params']>
export type AllFindOption<M = any> = FindOption<M>

export type NativeQueryFindOption<M = any> = NonNullable<CloudApiParams<M>['params']> & {
  onlyGenerateSql?: boolean
  /** 是否转换 json 字段，将 json 字符串转成 json 对象，填写字段名称*/
  convertJsonField?: string[]
  /** 是否自动转移字段名称，例如将结果的 user_name 转成 userName，默认开启 */
  underscored?:
  | boolean
  | {
    /** 是否转换查询参数，例如 userName 转成 user_name */
    options?: boolean
    /** 是否转换结果集，例如 user_name 转成 userName */
    result?: boolean
  }
}

/** 总的类型 */
export type RegisterFunction<E = any> = ((name: string, func: Function) => any) &
  ((entityName: keyof E, name: string, func: Function) => any)

/** 类型营造地 */
type CloudFunctionType = Partial<{
  Sequelize: any
  Entitys: any
  Params: any
  Redis: any
  Config: Record<string, any>
  Plugin: Record<string, any>
  State: Record<string, any>
  Env: Record<string, string>
  // 这个是自定义函数
  Execute?: (...args: any) => any
  // 这个是自定义函数
  Fn: Record<string, (params: any) => Promise<any>>
}>

/** 云函数 */
export interface CloudFunction<T extends CloudFunctionType = CloudFunctionType> {
  (
    request: {
      headers: IncomingHttpHeaders | any
      session?: Record<string, any>
      cookies: any | string
      userId?: string
      state: T['State']
      params: T['Params']
      /** url 查询参数 */
      query: Record<string, any>
    },
    context: {
      /** 表模型 */
      db: T['Entitys']
      /** 输出日志，类似 console */
      logger: typeof console
      /** 生成随机 id */
      generateId?: (num?: number) => string
      isDev: boolean
      /**
       * 可以在内部运行某云函数
       *
       * @deprecated 已启用，请使用新版本（v2.5.5+）的 $fn.getApp(parmas, options?)
       */
      $execute: (funcName: string, params?: any, opt?: any) => any
      /** 可以在内部运行某云函数 */
      $fn: T['Fn'] & Record<string, (...args: any) => any>
      /** sequelize 实例 */
      sequelize: T['Sequelize']
      /** redis 实例 */
      redis?: T['Redis']
      /** 环境变量 */
      env: T['Env']
      /** redis 配置 */
      redisOptions?: {
        /** 默认有 redis 实例时，会自动支持前端查询 redis，也可以手动关闭查询 */
        disableCloudQuery?: boolean
        prefix?: string
      }
      /** 配置 */
      config: T['Config']
      /** 插件 */
      plugin: T['Plugin']
      /** 资源权限 */
      dataPermission: {
        get: <K = DataPermissionKey | (string & {}) >(key: K | K[]) => any,
        getKeys: () => Promise<string[]>,
        getAll: () => Promise<Record<string, any>>,
      }
    }
  )
}

interface LikeConsole extends Console { }

export interface CloudServerOptions<E = any> {
  /** 模型的映射表，要求 key 是大写类名 */
  entityMap?: E
  isDev?: boolean
  /** 生成 ID 用 */
  generateId?: (num: number) => string
  /** 注册云函数 */
  registerFunction?: RegisterFunction<E>
  /** 注入 logger 函数，默认是 Console */
  logger?: LikeConsole
  /** 开启日志输出 */
  logging?: boolean
  /** 事务支持 */
  sequelize: Sequelize
  /** redis 连接 */
  redis?: Redis

  redisOptions?: {
    /** 默认有 redis 实例时，会自动支持前端查询 redis，也可以手动关闭查询 */
    disableCloudQuery?: boolean
    /** redis 查询前缀，默认无 */
    prefix?: string
  }

  /** 用于扩展 context 的插件，例如注入一些服务上下文等
   * plugin={ dataServer: ... }
   */
  plugin?: Record<string, any>
  config?: Record<string, any>

  /** ctx.session */
  session?: any
  /** ctx.headers */
  headers?: any
  /** ctx.session.user.id */
  userId?: string | null
  state?: Record<string, any>

  /** 跨库查询配置 */
  dbConfig?: {
    /** 数据库类型，默认 mysql */
    dbType?: string
    /** 是否允许开启只生成 sql */
    allowOnlyGenerateSql?: boolean
    /** 是否允许任意的数据库查询 */
    // allowAnyDbQuery?: boolean
    /** 数据库映射配置 */
    keyMap?: Record<string, { dbName: string, tableName: string }>,

    /**
     * 主应用的服务地址，设置了才能使用自动关联用户
     *
     * 类似 http://192.168.0.224:8000
     */
    mainAppOrigin?: string
    mainAppDbConfig?: {
      database?: string, // 数据库名称
      dialect?: string // 默认 mysql
      // 目前格式为 SugoUser: { tableName: 'sugo_user' }
      tableMap?: Record<string, {
        tableName: string
        [key: string]: any
      }>
      [key: string]: any
    }
  }

  /** 权限配置 */
  dataPermissionConfig?: {
    /** 是否开启 */
    enable?: boolean

    /** 权限表名称，默认是 sugo_data_permission，如果带 `.`，那就是锁死库名称 */
    tableName?: string
    /**
     * Model 名称与权限 type 的直接映射
     * 例如：{
     *   IndicesBaseModel: 'indicesRecord',
     *   // 默认关联的是 id，也可以指定关系
     *   ThemeSliceModel: { type: 'abiThemeAnalysis', targetFieldName: 'themeId'  },
     * }
     */
    keyMap?: Record<string, string | {
      /** 判断权限资源的类型 */
      type: string
      /** 判断有管理全部的类型，不指定，默认取 ${type}_admin */
      adminType?: string
      /** 关联字段 */
      targetFieldName?: string
      /** 创建人字段，默认值：created_by，默认查询创建人的，设置为 null 可屏蔽 */
      createdByFieldName?: string | null
    }>
  }
}

export interface LikeKoaCtx {
  /** 请求 body，类似 ctx.request.body */
  body?: any
  session?: any
  headers?: any
  userId?: string
  state?: Record<string, any>
  /** url 查询参数，类似 ctx.request.query */
  query?: Record<string, any>
  [key: string]: any
}

export type Ref = {
  message?: string,
  headers: Record<string, any>,
  session: Record<string, any>,
  query?: any
}

export type DataPermissionKey =
  | 'indicesAdmin'
  | 'indicesRecord'
  | 'indicesGroup'
  | 'indicesSubject'
  | 'indicesDimension'
  | 'indicesDimensionMembers'
  | 'indicesOrganization'
  | 'connectAdmin'
  | 'connectOrigin'
  | 'connectDb'
  | 'connectTable'
  | 'connectField'
  | 'connectRowConfig'
  | 'modelAdmin'
  | 'modelGroup'
  | 'modelRecord'
  | 'modelField'
  | 'modelRow'
  | 'analysisIndiceAdmin'
  | 'analysisIndice'
  | 'analysisIndiceGroup'
  | 'taskProjectAdmin'
  | 'taskProject'
  | 'tagAppProject'
  | 'tagAppGroup'
  | 'tagApp'
  | 'tagAppAdmin'
  | 'abiThemeAnalysis'
  | 'abiProject'
  | 'abiProjectAdmin'
  | 'abiThemeAnalysisAdmin'
  | 'abiThemeAnalysisGroup'
  | 'dataTableModel'
  | 'dataTableModelGroup'
  | 'dataTableModelAdmin'


