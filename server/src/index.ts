import _ from 'lodash'
import type { Sequelize } from 'sequelize'
import cookie from 'cookie'
import isjson from 'is-json'
import * as lz from 'lz-string'
import umiRequest from 'umi-request'

import { genId } from './utils'
import {
  getRedisFuncName,
  getRegisterArg
} from './utils'

import { CloudApiParamsConverter } from './convert'

import type { CloudServerOptions, CloudApiParams, LikeKoaCtx, RegisterFunction, Ref } from './type'

export { cloudParamsToNative } from './convert'
import { getDataPermissionFn, dataPermissionProxy } from './data-permission'

import { monitorPerformance, wrapColName, encryptData, decryptData } from './utils'

export { generateDataPermissionWhere, generateModelQuerySQL } from './sql'
export * as Consul from './consul'

export { genId } from './utils'

/** 云服务 */
export class CloudServer<Entity = any> {
  private opts: CloudServerOptions<Entity>
  private cacheCloudApiMap = {}
  private paramsConverter: CloudApiParamsConverter<Entity>
  private mainAppDbConfig = {} as {
    database?: string, // 数据库名称
    dialect?: string // 默认 mysql
    // 目前格式为 SugoUser: { tableName: 'sugo_user' }
    tableMap?: Record<string, any> // 数据表名称
    [key: string]: any
  }

  constructor(options: CloudServerOptions<Entity>) {
    this.opts = options
    this.paramsConverter = new CloudApiParamsConverter(options)
    this.mainAppDbConfig = options.dbConfig?.mainAppDbConfig || this.mainAppDbConfig
    const registerFunction = (options as any)?.registerFunction
    registerFunction?.((...args: any) => {
      this.register.apply(this, args)
    })
    // 内置注册获取权限接口
    this.register('getDataPermission', getDataPermissionFn)
  }

  /** 表实例 */
  public get db(): Record<string, any> {
    if (!_.isEmpty(this.opts.entityMap)) {
      return this.opts.entityMap as any
    }
    if (_.isArray(this._sequelize?.models)) {
      return _.keyBy(this._sequelize?.models || [], 'name')
    }
    const dict = this._sequelize?.models || {}
    if (_.isEmpty(dict)) {
      this._logger('error: sequelize.models is empty.')
    }
    return dict
  }

  private get _sequelize() {
    return this.opts.sequelize as Sequelize
  }

  private _logger(...args: any[]) {
    const func = this.opts.logger?.log ? this.opts.logger.log : console.log
    func?.('[sugo-sequelize-cloud]', ...args)
  }

  /** 执行 sequelize 函数 */
  private async _executeSequelize(body: CloudApiParams, ref?: Ref) {

    // 补充主应用关联的
    const relations = _.castArray(_.get(body, 'params.relations', []))
    const usePermission = _.get(body, 'params.usePermission')

    // 把主应用的数据库信息加载过来
    if (usePermission || !_.isEmpty(relations)) {
      // 先把主应用 db config 查询过来
      await this.loadMainAppDbConfig(ref?.headers || {})
    }

    const data = await this.paramsConverter.execFunction(body, ref, this.mainAppDbConfig)

    let result: any = null
    let rows: any[] = []

    const isFindAndCountAll = body.functionName === 'findAndCountAll'

    if (isFindAndCountAll) {
      result = {}
      result.list = _.map(data.rows, i => _.isFunction(i?.toJSON) ? i.toJSON() : i)
      result.total = data.count
      rows = result.list
    } else {
      result = _.isArray(data) ? _.map(data, i => _.isFunction(i?.toJSON) ? i.toJSON() : i) : data
      rows = result
    }

    if (!_.isEmpty(relations)) {
      const wrap = (str: string) => wrapColName(this.mainAppDbConfig.dialect || 'mysql', str)

      const fn = async (item: (typeof relations)[number]) => {
        const isLocal = /^#/.test(item.tableName)
        let table = _.get(this.mainAppDbConfig, ['tableMap', item.tableName])

        // # 开头的是本地库
        if (isLocal) {
          const t: any = _.get(this, `_sequelize.models[${_.replace(item.tableName, /^#/, '')}]`)
          if (t) {
            table = { tableName: t.getTableName() }
          }
        }

        if (!table || !table?.tableName) return false

        let database = this.mainAppDbConfig?.database || ''
        let tableName = table.tableName || ''

        const relatedName = wrap(item.relatedName || 'id')

        // 传进来的 tableName 有 . 时
        const hasDbbase = tableName.indexOf('.') > -1
        if (hasDbbase) {
          const arr = tableName.split('.')
          database = arr[0]
          tableName = arr[1]
        }

        let tb = hasDbbase ? tableName : `${wrap(database)}.${wrap(tableName)}`

        if (isLocal) {
          tb = wrap(tableName)
        }

        // 使用 dbMap 的配置
        const dbKeyMap = _.get(this.opts, ['dbConfig', 'keyMap', tableName])
        if (dbKeyMap) {
          tb = `${wrap(dbKeyMap.dbName)}.${wrap(dbKeyMap.tableName)}`
        }

        const sourceFieldNames: string[] = _.uniq(_.values(_.mapValues(item.fieldMapping, v => v.sourceName))).filter(i => !!i)
        const selectFields: string[] = _.uniq(_.flatMap(_.values(_.mapValues(item.fieldMapping, f => _.map(f.attributes, a => _.castArray(a)[0])))))
        const ids: string[] = _.uniq(_.flatMap(_.map(rows, i => _.values(_.pick(i, sourceFieldNames)))).filter(i => i))

        if (!_.isEmpty(ids) && !_.isEmpty(rows)) {

          const sql = `SELECT ${selectFields.length === 0 ? '*' : _.join(selectFields.map(i => wrap(i)), ', ')}
            FROM ${tb} WHERE ${relatedName} IN(${_.join(ids.map(i => `'${i}'`), ', ')})
          `
          const res = await this._sequelize.query(sql, { type: 'SELECT', raw: true }).catch(err => {
            console.error('[sugo-sequelize-cloud] relations Error', err.message)
          })

          // 加进列表里面
          const dataMap = _.keyBy(res || [], 'id') as Record<string, any>

          // 重写字段
          const rewriteField = d => {
            _.forEach(item.fieldMapping, (field, key) => {
              const target = key
              const source = field.sourceName
              const attributes = field.attributes || []
              if (!target) return

              const attrMap = _.reduce(attributes, (o, v) => {
                if (_.isArray(v)) return { ...o, [v[0]]: v[1] }
                return { ...o, [v]: v }
              }, {})

              const pick = _.isEmpty(attributes) ?
                dataMap[_.get(d, source)] :
                _.pick(dataMap[_.get(d, source)], _.map(attributes, a => _.castArray(a)[0]))

              _.set(d, target, _.mapKeys(pick, (_v, k) => attrMap[k] || k))
            })

            return d
          }

          // 设置结果
          if (_.isArray(rows) && /findAll|findAndCountAll/.test(body.functionName)) {
            _.forEach(rows, (_v, index) => {
              const item = isFindAndCountAll ? result.list[index] : result[index]
              rewriteField(item)
            })
          }
          if (result && /findByPk|findOne|findOrCreate/.test(body.functionName)) {
            result = rewriteField(_.isFunction(result?.toJSON) ? result.toJSON() : result)
          }
        }
      }

      await Promise.allSettled(_.map(relations, r => fn(r)))
    }

    return result
  }

  /** 执行 redis 函数 */
  private async _executeRedis(body: CloudApiParams) {
    const { functionName, data } = body
    if (!getRedisFuncName(functionName)) {
      throw new Error(`functionName(${functionName}) Illegal function operation.`)
    }
    // redis 的函数参数全放 params 了
    const redis = this.opts.redis
    if (!redis) {
      throw new Error('redis instance not created.')
    }
    const func = redis[functionName] as Function
    if (!func) {
      throw new Error(`redis operation function(${functionName}) does not exist`)
    }

    // 预处理 prefix key
    const prefix = this.opts.redisOptions?.prefix
    if (prefix) {
      if (data[0] && _.isString(data[0])) {
        data[0] = `${prefix}${data[0]}`
      }
      if (data[0] && _.isArray(data[0])) {
        data[0] = _.map(data[0], val => `${prefix}${val}`)
      }
    }

    if (this.opts.logging) {
      this._logger('redis args:', JSON.stringify({ name: functionName, data }, null, 2))
    }

    const res = await func.apply(redis, data)

    if (_.isString(res) && isjson(res)) {
      try {
        return JSON.parse(res)
      } catch (err) {
        return res
      }
    }

    return res
  }

  /** 执行云函数 */
  private async _executeCloud(modelName: string, funcName: string, ctx: LikeKoaCtx) {
    const func = this.cacheCloudApiMap[`${modelName}.${funcName}`]

    if (!func) {
      throw new Error(`cloud function(${modelName}.${funcName}) does not exist`)
    }
    const data = ctx.body || ({} as any)
    const query = ctx.query || ({} as any)

    if (this.opts.logging) {
      this._logger(`execute function: ${modelName}.${funcName}`)
      this._logger(`execute function args:`, JSON.stringify(data, null, 2))
    }

    const headers = ctx.headers || {}

    // TODO: ctx 可能只有 body
    const request = {
      headers: headers || this.opts.headers,
      session: ctx.session || this.opts.session,
      cookies: !!headers['cookie'] ? cookie.parse(headers['cookie']) : {},
      userId:
        _.get(headers, ['u-id']) ||
        ctx.userId ||
        this.opts?.userId ||
        _.get(this.opts.session, 'user.id') ||
        _.get(ctx, 'session.user.id'),
      state: ctx.state || this.opts.state || {},
      params: data.params,
      query
    }

    const self = this

    const $fn = new Proxy<any>({}, {
      get: (_target, key) => {
        return function (params: any) {
          const { namespace, name, func } = getRegisterArg([key, params])
          const newCtx = { ...ctx, body: { ...ctx.body, params: func } }
          return self._executeCloud.call(self, namespace, name, newCtx)
        }
      }
    })

    const context = {
      db: this.db,
      logger: this.opts.logger || console,
      isDev: this.opts.isDev === undefined ? process.env.NODE_ENV !== 'production' : this.opts.isDev,
      generateId: this.opts.generateId || genId,
      sequelize: this._sequelize,
      redis: this.opts.redis,
      redisOptions: this.opts.redisOptions,
      env: process.env,
      plugin: this.opts.plugin || {},
      $execute: (...args) => {
        const { namespace, name, func } = getRegisterArg(args)
        const newCtx = { ...ctx, body: { ...ctx.body, params: func } }
        return self._executeCloud.call(self, namespace, name, newCtx)
      },
      $fn,
      config: this.opts.config || {},
      /** 资源权限 */
      dataPermission: dataPermissionProxy(request.headers, request.session, this.opts.redis)
    }

    if (!_.isFunction(func)) return null

    return func(request, context)
  }

  /**
   * 加载主应用的数据库配置
   */
  private async loadMainAppDbConfig(headers: Record<string, any>) {
    const host = this.opts.dbConfig?.mainAppOrigin
    if (!host) return
    if (this.mainAppDbConfig?.database) return
    // 请求远程主应用配置
    const mainAppDbConfig = await umiRequest.get(`${_.replace(host, /\/$/, '')}/common/get-db-config`, {
      headers: _.pick(headers, ['cookie'])
    }).catch()
    this.mainAppDbConfig = {
      ...this.mainAppDbConfig,
      ...mainAppDbConfig,
    }
    // 如果主应用配置了，就合并过来
    if (this.opts.dbConfig?.mainAppDbConfig?.database) {
      this.mainAppDbConfig = {
        ...this.mainAppDbConfig,
        ...this.opts.dbConfig?.mainAppDbConfig,
      }
    }
  }

  /** 对 body 进行解密 */
  public static decryptBody(headers: any, body: CloudApiParams) {
    if (_.isEmpty(body)) return body

    const encryption = headers['x-encryption']
    // 如果需要加密，并且未解密，就解密
    if (!(encryption && encryption !== '0')) return body
    if (headers['x-decrypted']) return body // 解密了

    if (_.isArray(body)) {
      return _.map(body, item => CloudServer.decryptBody(headers, item)) as CloudApiParams[]
    } else {
      // 目前只有这两个加密了，先解开
      if (body.data && _.isString(body.data)) {
        _.set(body, 'data', decryptData(body.data, encryption))
      }
      if (body.params && _.isString(body.params)) {
        _.set(body, 'params', decryptData(body.params, encryption))

      }
    }

    return body
  }

  /** 加密内容 */
  public static encryptResult(headers: any, result: any) {
    // if (_.isEmpty(result)) return result
    const encryption = headers['x-encryption']
    // 如果需要加密，并且未解密，就解密
    if (!(encryption && encryption !== '0')) return result

    const sign = String(Date.now())

    return encryptData(result, encryption, sign)
  }

  /**
   * @deprecated 此函数即将废弃，请使用新的方法 query 替代。
   * @deprecated 弃用
   * 对外提供的查询函数
   */
  public async execKoa(ctx: LikeKoaCtx) {
    return this.query(ctx)
  }

  /** 内部查询函数 */
  private async _query(ctx: LikeKoaCtx) {
    // 如果需要加密，并且未解密，就解密
    const body = CloudServer.decryptBody(ctx.headers, ctx.body) as CloudApiParams
    const ref: Ref = {
      headers: ctx.headers,
      session: ctx.session
    }

    if (_.isEmpty(body)) throw new Error('body does not exist.')

    // 补充用户 id
    body.userId = ctx.headers['u-id']

    let result

    if (!_.isArray(body)) {
      if (body.namespace === '$use') {
        result = await this.paramsConverter.executeNativeDbQuery(body)
        return { success: true, result: CloudServer.encryptResult(ctx.headers, result) }
      }

      if (body.namespace === '$cloud') {
        result = await this._executeCloud(body.modelName, body.functionName, ctx)
        return { success: true, result: CloudServer.encryptResult(ctx.headers, result) }
      }

      // 转到 redis 处理
      if (body.namespace === '$redis') {

        if (this.opts.redisOptions?.disableCloudQuery) {
          return { success: false, result: null, message: '不支持前端的 redis 查询（disableCloudQuery=true）' }
        }

        result = await this._executeRedis(body)
        return { success: true, result: CloudServer.encryptResult(ctx.headers, result) }
      }
    }

    // 批量执行
    if (_.isArray(body)) {
      result = []
      // 如果有传 sequelize 就是包在事务里，并且需要含有 create/update/delete 等语句
      if (this._sequelize && _.some(body, i => /create|update|upsert|delete/g.test(i.functionName))) {
        // 事务提交
        const t = await this._sequelize.transaction()
        try {
          for (const b of body) {
            if (!b.params) b.params = {}
            b.params.transaction = t // 把事务传进去
            let res = await this._executeSequelize(b, ref)
            if (b.params?.returning === false) {
              res = { id: result?.id }
            }
            result.push(res)
          }
          await t.commit() // 提交事务
          return { success: true, result: CloudServer.encryptResult(ctx.headers, result) }
        } catch (err: Error | any) {
          console.error(err)
          await t.rollback() // 回滚事务
          return { success: false, error: err.message }
        }
      } else {
        // 非事务的
        for (const b of body) {
          let res = await this._executeSequelize(b, ref)
          if (b.params?.returning === false) {
            res = { id: result?.id }
          }
          result.push(res)
        }
        return { success: true, result: CloudServer.encryptResult(ctx.headers, result) }
      }
    }

    result = await this._executeSequelize(body, ref)

    if (body.params?.returning === false) {
      result = { id: result?.id }
    }

    return {
      success: true,
      result: CloudServer.encryptResult(ctx.headers, result),
      message: ref.message
    }
  }

  /** 对外提供的查询函数 */
  public async query(ctx: LikeKoaCtx) {
    const { namespace, modelName, functionName } = ctx.body || {}
    const key = `${namespace || '$'}.${modelName}.${functionName}`

    // 设置参数
    if (ctx) this.setOptions(_.pick(ctx, ['headers', 'session', 'userId', 'state']))

    const fn = monitorPerformance(this._query.bind(this), key)
    const [info, result] = await fn(ctx)


    if (typeof result === 'object') {
      result.date = new Date()
      result.ms = info?.duration
    }

    return result
  }

  /**
   * 执行云函数，类似 $execute(name)
   *
   * @deprecated 已启用，请使用新版本（v2.5.5+）的 $fn.getApp(parmas, options?)
   */
  public async $execute(...args: any[]) {
    const { namespace, name, func } = getRegisterArg(args)
    const ctx: any = {
      body: { params: func }
    }
    if (args.length >= 3) {
      ctx.body.params = func
    }
    return this._executeCloud(namespace, name, ctx)
  }

  /**
   * 执行云函数，类似 $fn.getApp({})
   */
  public $fn = new Proxy<any>(
    {},
    {
      get: (_target, key) => {
        const self = this
        return function (params: any) {
          const { namespace, name, func } = getRegisterArg([key, params])
          const ctx: any = {
            body: { params: func }
          }
          const newCtx = { ...ctx, body: { ...ctx.body, params: func } }
          return self._executeCloud.call(self, namespace, name, newCtx)
        }
      }
    }
  )

  /** 设置配置项 */
  public setOptions(options: Partial<CloudServerOptions<Entity>>) {
    for (const key in options) {
      this.opts[key] = options[key]
    }
    return this
  }

  /** 公开的注册云函数 */
  public register: RegisterFunction<Entity> = (...args: any[]) => {
    const { namespace, name, func } = getRegisterArg(args)

    this.cacheCloudApiMap[`${namespace}.${name}`] = func
    this.cacheCloudApiMap[`${namespace}.${_.camelCase(name)}`] = func // 对应生成驼峰版本

    if (this.opts.logging) {
      this._logger('register:', `${namespace}.${name}`)
    }
    return this
  }
}
