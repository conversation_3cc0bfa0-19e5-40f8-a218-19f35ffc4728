import type { CloudApiParams, CloudServerOptions, NativeQueryFindOption, Ref } from './type'
import type { FindOptions, Sequelize, WhereOptions } from 'sequelize'
import { Op } from 'sequelize'
import _ from 'lodash'
import vm from 'vm'

import { getDataPermission, getUserRedisInfo } from './data-permission'

import {
  getSequelizeFuncName,
  OP_MAP,
  isJSONOpKey,
  generateSql,
  convertToSnakeCaseByOptions,
  convertToDatabaseName,
  convertToResult,
  convertToCountGroup,
  queryData,
} from './utils'

import { generateDataPermissionWhere } from './sql'

/**
 * 原始查询才用到的
 */
export class CloudApiParamsConverter<Entity> {
  private minSeq: Sequelize | null = null

  constructor(options: CloudServerOptions<Entity>) {
    this.opts = options
  }

  private opts: CloudServerOptions<Entity>

  private get sequelize() {
    return this.opts.sequelize as Sequelize
  }

  /** 表实例 */
  public get db() {
    if (!_.isEmpty(this.opts.entityMap)) {
      return this.opts.entityMap
    }
    if (_.isArray(this.sequelize?.models)) {
      return _.keyBy(this.sequelize?.models || [], 'name')
    }
    const dict = this.sequelize?.models || {}
    if (_.isEmpty(dict)) {
      this.logger('error: sequelize.models is empty.')
    }
    return dict as Record<string, any>
  }

  private get Sequelize() {
    return this.opts.sequelize.Sequelize
  }

  // 几个特殊执行的函数
  private get sequelizeFn() {
    return {
      col: this.sequelize.col.bind(this.sequelize),
      fn: this.sequelize.fn.bind(this.sequelize),
      literal: this.sequelize.literal.bind(this.sequelize),
      where: this.sequelize.where.bind(this.sequelize),
      json: this.sequelize.json.bind(this.sequelize)
    }
  }

  private logger(...args: any[]) {
    const func = console.log
    func?.('[sugo-sequelize-cloud]', ...args)
  }

  /**
   * 获取权限数据，返回 false 表示是超管，返回 ids 数组，如果带有 xxxAdmin: true 的就是有管理权限
   *
   * @deprecated 弃用
   */
  private async getDataPermissionIds(modelName: string) {
    const cif = _.get(this.opts, `dataPermissionConfig.keyMap[${modelName}]`)
    const type = _.camelCase((_.isObject(cif) ? cif.type : cif) || '')

    // 没有匹配到对应的表
    if (!type || type === 'null' || type === 'undefined') return false

    const dataMap = await getDataPermission({ type }, this.opts.headers, this.opts.session, this.opts.redis)
    if (dataMap.isAdmin) return false
    // 如果带有 xxxAdmin: true 的就是有管理权限

    const prefix = _.snakeCase(type).split('_')[0] || ''
    const accessibleUserIds = dataMap.accessibleUserIds || []

    if (dataMap[prefix] === true) return false
    if (dataMap[`${type}Admin`] === true) return false

    const list = dataMap[type] || []
    const ids: string[] = _.uniq(_.map(list, i => {
      if (_.isString(i)) return i
      if (_.isObject(i as any)) return i.id
      return ''
    }).filter(i => i))

    return { ids, accessibleUserIds }
  }

  // 获取对应的执行函数
  public getDbFunction(entityName: string, functionName: string) {
    const entity = this.db?.[entityName]
    if (!entity) {
      throw new Error(`entity(${entityName}) does not exist.`)
    }

    if (!getSequelizeFuncName(functionName)) {
      throw new Error(`functionName(${functionName}) Illegal function operation.`)
    }

    const func = entity[functionName]
    if (!func) throw new Error(`functionName(${functionName}) does not exist.`)

    return func.bind(entity)
  }

  // 将字符串形式的原始查询转成对象
  // where(literal("MATCH('content', AGAINST('${keyword}' IN BOOLEAN MODE))"), '>', 0)
  public convertExpression(code: string) {

    const sandbox = {
      ...this.sequelizeFn,
      result: null
    }
    // 这里每次都重新 new，是否需要优化，减少资源消耗
    const script = new vm.Script(`result = ${code}`)
    const context = vm.createContext(sandbox)
    script.runInContext(context)

    return sandbox.result
  }

  // 转换 where { id: { $in: [1, 2, 3] } } => { id: { [Op.in]: [1, 2, 3] } }
  public convertWhere(where: any) {
    if (_.isEmpty(where)) return where // {} []
    if (_.isArray(where)) return where.map(i => this.convertWhere(i))
    const obj = {}

    for (let key in where) {
      const value = where[key]
      // 替换键
      if (OP_MAP[key]) key = OP_MAP[key] // OP.xxx
      // ...
      obj[key] = this.checkAndExceOpFn(value)

      // 值是数组，尝试递归
      if (_.isArray(value)) {
        obj[key] = value.map(i => {
          if (_.isObject(i)) return this.convertWhere(i)
          return i
        })
      }

      const opKeys = _.keys(obj[key])

      // 转换特殊的 json 查询
      if (_.isObject(obj[key]) && opKeys.length === 1 && isJSONOpKey(opKeys[0])) {
        const op = opKeys[0]
        obj[key] = this.convertJSONOpToSQL(key, op, obj[key][op])
      }
      // 对象非数组，递归吧
      else if (!_.isArray(value) && _.isObject(value)) {
        obj[key] = this.convertWhere(value)
      }
    }

    return obj
  }

  // 转换 include，将字符串变成类对象
  public convertInclude(include: string | any) {
    if (_.isString(include)) return this.db?.[include]
    if (_.isArray(include)) {
      return _.map(include, item => {
        const newItem = this.convertInclude(item)
        if (item.include) newItem.include = this.convertInclude(item.include)
        return newItem
      })
    }
    if (_.isObject(include) as any) {
      return {
        ...include,
        attributes: include.attributes ? this.convertAttributes(include.attributes) : undefined,
        model: this.db?.[include.model],
        where: this.convertWhere(include.where)
      }
    }
  }

  // 转换 attributes
  public convertAttributes(attributes: string[][] | string[] | { include: string[][] | string[], exclude: string[][] | string[] }) {
    const value = _.cloneDeep(attributes)
    if (_.isObject(value) && !_.isArray(value)) {
      if (value.include) value.include = this.convertAttributes(value.include) as any
      if (value.exclude) value.exclude = this.convertAttributes(value.exclude) as any
    }
    if (_.isArray(value)) {
      _.forEach(value, (item, index) => {
        if (!_.isArray(item)) return
        _.forEach(item, (attr, idx) => {
          _.set(value, `[${index}][${idx}]`, this.checkAndExceOpFn(attr))
        })
      })
    }

    return value
  }

  // 转换 GroupBy
  public convertGroupBy(groupBy: string[]) {
    const list = _.filter(groupBy, i => i)
    return list.map(key => this.checkAndExceOpFn(key))
  }

  // 将 mysql 不支持的操作符转换成 JSON 操作
  public convertJSONOpToSQL(field: string, op: string, value: any) {
    if (this.sequelize?.getDialect() !== 'mysql') return value
    const { col, fn, where } = this.sequelizeFn
    field = _.snakeCase(field)

    if (_.isArray(value)) {
      switch (op) {
        case '$contains': return where(fn('JSON_CONTAINS', col(field), JSON.stringify(value)), true)
        case '$overlap': return where(fn('JSON_OVERLAPS', col(field), JSON.stringify(value)), true)
        case '$contained': return where(fn('JSON_CONTAINED', col(field), JSON.stringify(value)), true)
        case '$adjacent': return where(fn('JSON_ADJACENT', col(field), JSON.stringify(value)), true)
        case '$strictLeft': return where(fn('JSON_CONTAINED', fn('JSON_EXTRACT', col(field), '$[0]'), JSON.stringify(value)), true)
        case '$strictRight': return where(fn('JSON_CONTAINED', fn('JSON_EXTRACT', col(field), `$[${_.size(value) - 1}]`), JSON.stringify(value)), true)
        case '$noExtendLeft': return where(fn('JSON_CONTAINED', fn('JSON_REMOVE', col(field), '$[0]'), JSON.stringify(value)), true)
        case '$noExtendRight': return where(fn('JSON_CONTAINED', fn('JSON_REMOVE', col(field), `$[${_.size(value) - 1}]`), JSON.stringify(value)), true)
        default: return value
      }
    }

    return value
  }

  // 转换原始查询
  public checkAndExceOpFn(value: any) {
    if (_.isString(value) && /^col\(|^where\(|^literal\(|^fn\(|^json\(/.test(value)) {
      if (!this.sequelize) {
        console.error('你没有提供 sequelize，因此你不能用 literal() 等函数语法')
      } else {
        return this.convertExpression(value)
      }
    }
    return value
  }

  // 解析 params 对齐参数
  public convertParamsToQuery(params: CloudApiParams['params']) {

    const transaction = params?.transaction
    if (transaction) {
      delete params?.transaction
    }

    const query: FindOptions & Record<string, any> = {
      _iscloud: true,
      _usePermission: !!params.usePermission
    }

    // 排序 order: { title: 'ASC' } => order: [['title', 'ASC']]
    if (params.order) {
      if (_.isObject(params.order)) {
        query.order = _.keys(params.order).map(key => [key, params.order![key]])
      }
      if (_.isArray(params.order)) {
        query.order = params.order as any
      }
      query.order = _.map(query.order, o => [this.checkAndExceOpFn(o[0]), o[1]]) as any[]
    }

    if (params.include) {
      query.include = this.convertInclude(params.include)
    }

    if (params.distinct !== undefined) {
      query.distinct = params.distinct
    }

    // 选择和排除属性
    if (params.attributes) {
      query.attributes = this.convertAttributes(params.attributes as string[]) as any
    }

    // 分页，最大取 10000 项
    if (_.isNumber(params.limit) && params.limit >= 0) {
      query.limit = Math.min(params.limit, 10000)
    }
    if (_.isNumber(params.offset) && params.offset >= 0) {
      query.offset = params.offset
    }

    // todo: 这个字段将抛出，设计错误
    if (params.groupBy) {
      query.group = this.convertGroupBy(params.groupBy as string[])
    }
    // 分组
    if (params.group) {
      query.group = this.convertGroupBy(params.group as string[])
    }

    if (params.where) {
      query.where = this.convertWhere(params.where)
    }
    if (params.having) {
      query.having = this.convertWhere(params.having)
    }
    if (transaction) {
      query.transaction = transaction
    }
    if (params.paranoid !== undefined) {
      query.paranoid = params.paranoid
    }
    if (params.returning !== undefined) {
      query.returning = params.returning
    }

    return query
  }

  /** 资源权限自动填充 id，生成一个 $and
   *
   * @deprecated 弃用
  */
  private async fixDataPermissionWhere(modelName: string, functionName: string, where: WhereOptions | any, usePermission: boolean | string, userId?: string) {
    const cif = _.isString(usePermission) ? usePermission : _.get(this.opts, `dataPermissionConfig.keyMap[${modelName}]`)
    // 过滤 id
    let message: string | undefined
    if (/findAll|findOne|findAllAndCount|findAndCountAll|update|findOrCreate|upsert|delete|findByPk|updateByPk/i.test(functionName)) {
      const auth = await this.getDataPermissionIds(modelName)
      const ID = (_.isObject(cif) ? cif.targetFieldName : 'id') || 'id' // 默认 id

      // 增加的条件，id 和 createdBy

      if (auth !== false && ID) {
        const { ids, accessibleUserIds } = auth

        let createdBy: any = undefined
        if (!_.isEmpty(accessibleUserIds)) {
          createdBy = { createdBy: { [Op.in]: [...accessibleUserIds].filter(i => i) } }
        }
        if (userId) {
          createdBy = { createdBy: { [Op.in]: [userId, ...accessibleUserIds].filter(i => i) } }
        }

        // id 和 createdBy 是 or 条件
        const authWhere = createdBy ? { [Op.or]: [{ [ID]: { [Op.in]: ids } }, createdBy] } : { [ID]: { [Op.in]: ids } }

        if (_.isEmpty(ids) && _.isEmpty(accessibleUserIds)) message = '资源无授权权限，只有自己创建的'

        if (_.isEmpty(where)) {
          where = authWhere
        }
        else if (_.isArray(where)) { // 是一个数组时
          where = {
            [Op.and]: [...where, authWhere]
          }
        }
        else if (!where[ID]) { // 不存在就直接合并到对象
          where = { ...where, ...authWhere }
        }
        else { // 否则就合并到 $and
          where = {
            [Op.and]: [where, authWhere]
          }
        }
      }
    }

    return { where, message }
  }

  /**
   * 关联数据权限
   * @param modelName
   * @param functionName
   * @param permissionTableName 权限表
   * @param where
   * @param headers
   * @param session
   * @returns
   */
  private async relatedDataPermissionWhere(modelName: string, functionName: string, permissionTableName: string, where: WhereOptions | any,  headers: Record<string, any>, session: Record<string, any>) {
    // 根据 modelName 找出权限的 type 值
    // 如果是超管则返回全部
    // 如果是有 ${type}_admin 行，那么返回全部

    // 基本全部都要限制，如果没有权限，那么 update/delete 会失败
    if (!/findAll|findOne|findAllAndCount|findAndCountAll|findOrCreate|update|upsert|delete|findByPk|updateByPk|count|find|bulkCreate/.test(functionName)) return where

    // 1. 获取用户信息
    const user = await getUserRedisInfo(headers, session, this.opts.redis)
    // 超管
    if (user.isSuperAdmin) return where

    // 2. 获取用户 id，角色 id，机构 id
    const userId: string = _.get(user, 'id', undefined)
    const roleIds: string[] = _.get(user, 'accessibilityByInstitution.roleIds', [])
    const orgIds: string[] = _.get(user, 'accessibilityByInstitution.institutionIds', [])

    let targetFieldName = 'id'
    let type = _.get(this.opts, `dataPermissionConfig.keyMap.${modelName}`)
    // 获取数据库类型
    const dbType = this.opts.dbConfig?.dbType || 'mysql'
    let adminType = `${type}_admin`
    let createdByFieldName: string | null = null // createdBy
    if (_.isObject(type)) {
      targetFieldName = type.targetFieldName || ''
      adminType = type.adminType || adminType
      createdByFieldName = type.createdByFieldName || null
      type = type.type
    }

    return generateDataPermissionWhere(where, {
      userId,
      orgIds,
      roleIds,
      modelName,
      permissionTableName: permissionTableName,
      type: type as string,
      adminType,
      createdByFieldName,
      targetFieldName,
      dbType
    })
  }

  /**
   * 处理和调用 sequelize 的查询函数
   *
   * 执行云函数入口
   *
   */
  public async execFunction(apiParams: CloudApiParams, ref?: Ref, mainAppDbConfig?: any) {
    const { modelName, functionName, data } = apiParams
    let params = apiParams.params || {}

    const func = this.getDbFunction(modelName, functionName)
    // 转换参数，转成 sequelize 的条件
    const query = this.convertParamsToQuery(params)

    // 权限控制
    if (params.usePermission === 'new' && this.opts.dataPermissionConfig?.enable) {
      const tableName = String(this.opts.dataPermissionConfig?.tableName || 'sugo_data_permission')
      const permissionTableName = tableName.indexOf('.') > -1 ? tableName : `${mainAppDbConfig?.database}.${tableName}`
      // 关联数据查询
      query.where = await this.relatedDataPermissionWhere(modelName, functionName, permissionTableName, query.where, ref?.headers || {}, ref?.session || {})
    }
    /**
     * 支持资源授权的权限
     * @deprecated 弃用
     */
    else if ((_.isString(params.usePermission) && !!params.usePermission) || (this.opts.dataPermissionConfig?.enable && !!params.usePermission)) {
      const res = await this.fixDataPermissionWhere(modelName, functionName, query.where, params.usePermission, apiParams.userId)
      query.where = res.where
      if (ref) ref.message = res.message
    }

    // 下面把参数放到 function 里面
    const args: any[] = []

    if (/find/.test(functionName) && ((query.limit || 0) < 0 || query.limit === undefined)) {
      query.limit = 1000
    }

    // 特殊处理
    if (functionName.indexOf('create') > -1) {
      args[0] = data
      args[1] = query
    } else if (functionName.indexOf('update') > -1 || functionName.indexOf('upsert') > -1) {
      if (!query.where) throw new Error('where does not exist.')
      // 判断 value 是否有操作函数
      args[0] = _.mapValues(data, val => this.checkAndExceOpFn(val))
      args[1] = { ..._.omit(query, 'limit') }
    } else if (functionName.indexOf('findByPk') > -1) {
      args[0] = _.get(query, 'where.id') || data
      args[1] = _.omit(query, 'where')
    } else if (functionName.indexOf('bulkCreate') > -1) {
      args[0] = data
      args[1] = params
    } else {
      args[0] = query
    }

    if (ref) {
      ref.query = query
    }

    try {
      if (!_.isEmpty(args)) return func(...args)
      return null
    } catch (err) {
      this.logger('args:', JSON.stringify(args, null, 2))
      this.logger('error:', err)
      throw err
    }
  }

  // 初始化原生的实例
  private initNativeDbSequelizeInstance() {
    if (!this.minSeq && this.Sequelize && this.sequelize) {
      // 创建一个空连接
      this.minSeq = new this.Sequelize({
        dialect: this.sequelize.getDialect(),
        host: '',
        port: '',
        username: '',
        password: '',
        database: '',
        logging: false // 禁用日志输出
      })
    }
    if (!this.minSeq) throw new Error('create native query instance fail')
  }

  // 获取原生的 db 信息
  private getNativeDbInfo(config: CloudApiParams['config'] = {}) {
    // 如果带有 body.config.mapDbName 就是要去找映射
    let dbName = config.dbName
    let tableName = config.tableName
    if (config.mapDbName) {
      dbName = this.opts.dbConfig?.keyMap?.[config.mapDbName]?.dbName
      tableName = this.opts.dbConfig?.keyMap?.[config.mapDbName]?.tableName
    }

    if (!config.mapDbName) throw new Error('please configure the mapping table')
    if (!dbName || !tableName) throw new Error('dbName or tableName is empty')

    return { dbName, tableName }
  }

  /** 原生的数据库查询 */
  async executeNativeDbQuery(body: CloudApiParams) {
    this.initNativeDbSequelizeInstance()

    let options = _.cloneDeep(body.params) as NativeQueryFindOption<any>

    const functionName = body.functionName
    const isOnlyOneData = /findOne|findByPk/.test(functionName)
    const { dbName, tableName } = this.getNativeDbInfo(body.config || {})

    // 设置默认值
    if (options.underscored === undefined) {
      options.underscored = true
    }

    // 对 options 进行转成，将小驼峰转成带下划线的
    if (options.underscored === true || (options.underscored as any)?.options === true) {
      options = convertToSnakeCaseByOptions(_.cloneDeep(options)) as any
      // console.log('options:', options)
    }

    if (functionName === 'count') {
      options = convertToCountGroup(options)
    }

    if (options.attributes) {
      options.attributes = this.convertAttributes(options.attributes as string[]) as any[]
    }
    if (options.where) {
      options.where = this.convertWhere(options.where)
    }
    if (options.group) {
      options.group = this.convertGroupBy(options.group as string[])
    }
    if (options.having) {
      options.having = this.convertWhere(options.having)
    }

    // 强行设置为 1
    if (isOnlyOneData) {
      options.limit = 1
    }

    // 生成 sql
    let sqlQuery = generateSql(this.minSeq, tableName, options)
    if (this.sequelize.getDialect() !== 'sqlite') {
      sqlQuery = convertToDatabaseName(sqlQuery, dbName) // 新增库名称
    }

    // 只生成不执行
    if (!this.opts.dbConfig?.allowOnlyGenerateSql && options.onlyGenerateSql) {
      throw new Error('directly generating SQL is not allowed, function not enabled')
    }
    if (this.opts.dbConfig?.allowOnlyGenerateSql && options.onlyGenerateSql) {
      return sqlQuery
    }

    const result = await queryData(this.sequelize, functionName, sqlQuery)

    // ---------------------- 返回结果 -----------------------------
    return convertToResult(functionName, options, result)
  }
}

/**
 * 将云函数的 params 转成 sequelize 原生的参数
 */
export const cloudParamsToNative = <T = any>(params: CloudApiParams<T>['params'], sequelize?: Sequelize) => {
  const cap = new CloudApiParamsConverter<T>({ sequelize: sequelize })
  const result = cap.convertParamsToQuery(params)

  return {
    ...result,
    limit: params.limit
  }
}
