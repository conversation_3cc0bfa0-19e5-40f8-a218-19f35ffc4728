import { Op, Sequelize } from 'sequelize'
import _ from 'lodash'
import isJson from 'is-json'
import crypto from './encrypt'
// import { Redis } from 'ioredis'

// const res = new Redis()
// res.decrby()

import { customAlphabet } from 'nanoid'

// TODO: 10 位数
export const nanoid = customAlphabet('1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 10)
export const genId = (n?: number) => nanoid(n || 10)

import type { NativeQueryFindOption } from './type'

/**
 * @type {import('perf_hooks')}
 */
let perfHooks
try {
  perfHooks = require('perf_hooks')
} catch (e) {
  console.error('Error: perf_hooks module not supported in this environment.')
  console.error(e)
}

/** 支持 Sequelize 支持调用的函数 */
export const getSequelizeFuncName = (name?: string) => {
  const list = [
    'findAll',
    'findAndCountAll',
    'findOne',
    'findOrCreate',
    'findByPk',
    'create',
    'update',
    'upsert',
    'count',
    'bulkCreate',
    'delete',
    'destroy'
  ]
  if (name) return list.includes(name)
  return list
}

/** 获取 redis 支持调用的函数 */
export const getRedisFuncName = (name?: string) => {
  const list = [
    'keys', 'scan',
    // string
    'get', 'set', 'del',
    // 'keys',
    'mget', 'mset',
    // hash
    'hdel', 'hget', 'hmget', 'hset', 'hmset', 'hgetall', 'hkeys', 'hlen', 'hvals', 'hexists', 'hstrlen', 'hsetnx',
    // list
    'lindex', 'linsert', 'llen', 'lrange', 'lrem', 'lset', 'lpush', 'lpop', 'lmove', 'rpop', 'rpush',
    // set
    'sadd', 'srem', 'spop', 'scard', 'smembers', 'sdiff', 'sinter', 'sunion',
    // zset
    'zadd', 'zrange', 'zrank', 'zcard', 'zcount',
    // +1, -1 原子操作
    'incr', 'incrby', 'incrbyfloat', 'decr', 'decrby',
  ]
  if (name) return list.includes(name)
  return list
}

export const OP_MAP = {
  '$and': Op.and,
  '$or': Op.or,
  '$not': Op.not,
  '$between': Op.between,
  '$notBetween': Op.notBetween,
  '$in': Op.in,
  '$notIn': Op.notIn,
  '$like': Op.like,
  '$notLike': Op.notLike,
  '$iLike': Op.iLike,
  '$notILike': Op.notILike,
  '$regexp': Op.regexp,
  '$notRegexp': Op.notRegexp,
  '$iRegexp': Op.iRegexp,
  '$notIRegexp': Op.notIRegexp,
  '$overlap': Op.overlap,
  '$contains': Op.contains,
  '$contained': Op.contained,
  '$any': Op.any,
  '$all': Op.all,
  '$eq': Op.eq,
  '$ne': Op.ne,
  '$gte': Op.gte,
  '$gt': Op.gt,
  '$lte': Op.lte,
  '$lt': Op.lt,
  '$is': Op.is,
  '$col': Op.col
}

/** 是否是 json 操作符 */
export const isJSONOpKey = (key: string) => [
  '$contains',
  '$overlap',
  '$contained',
  '$adjacent',
  '$strictLeft',
  '$strictRight',
  '$noExtendLeft',
  '$noExtendRight',
].includes(key)


/**
 * 生成 typescript 的类型
 */
export function generateTsTypes(metadata: Record<string, any>) {
  let types = ''

  for (const tableName in metadata) {
    const columns = metadata[tableName]
    const interfaceName = tableName.charAt(0).toUpperCase() + tableName.slice(1)

    types += `interface ${interfaceName} {\n`

    for (const columnName in columns) {
      const column = columns[columnName]
      let type = 'any'

      // 这个跳过
      if (column.fieldName === 'deletedAt') continue

      if (['INTEGER', 'BIGINT', 'FLOAT', 'DOUBLE'].includes(column.type)) type = 'number'
      if (['STRING', 'TEXT'].includes(column.type)) type = 'string'
      if (['BOOLEAN'].includes(column.type)) type = 'boolean'
      if (['DATE', 'DATEONLY', 'TIME', 'NOW'].includes(column.type)) type = 'Date'
      if (['JSON'].includes(column.type)) {
        if (_.isEqual(column.defaultValue, [])) {
          type = 'any[]'
        } else if (_.isEqual(column.defaultValue, {})) {
          type = 'Record<string, any>'
        } else {
          type = 'any'
        }
      }

      if (type === 'Date') {
        type = 'Date | string'
      }

      let isRequire = !column.allowNull
      if (['id', 'createdAt', 'updatedAt'].includes(column.fieldName)) {
        isRequire = false
      }

      // 补充注释
      let comment = '  '
      if (column.comment) {
        comment = `  /** ${column.comment} */\n  `
      }

      types += `${comment}${columnName}${isRequire ? '?' : ''}: ${type}\n`
    }

    types += '}\n\n'
  }

  return types
}


/** 将下划线转成小驼峰 */
export function convertToCamelCase(data) {
  if (_.isArray(data)) return _.map(data, obj => _.mapKeys(obj, (_v, key) => _.camelCase(key)))
  return _.mapKeys(data, (_v, key) => _.camelCase(key))
}

/*
 * 将 options 里的 where，attributes，groupBy，order 里的小驼峰命名
 * 定义一个函数，将一个对象里 where，attributes，groupBy，order 里的小驼峰命名，转换成下划线命名
 */
export function convertToSnakeCaseByOptions(options) {
  function camelToSnakeCase(input) {
    const parts = input.split('.')
    const convertedParts = parts.map(part => {
      if (part.match(/user[A-Z]/)) {
        return part.replace(/([A-Z])/g, '_\$1').toLowerCase()
      }
      return part
    })
    return convertedParts.join('.')
  }

  function convertKeys(obj) {
    if (_.isPlainObject(obj)) {
      return _.mapValues(_.mapKeys(obj, (_v, k) => camelToSnakeCase(k)), convertKeys)
    } else if (_.isArray(obj)) {
      return obj.map(convertKeys)
    } else if (_.isString(obj)) {
      return camelToSnakeCase(obj)
    } else {
      return obj
    }
  }

  const result = _.mapValues(options, (value, key) => {
    if (['where', 'attributes', 'groupBy', 'order'].includes(key)) {
      if (key === 'order' && !_.isArray(value)) {
        return _.mapKeys(value, (_v, k) => camelToSnakeCase(k))
      } else {
        return convertKeys(value)
      }
    } else {
      return value
    }
  })

  return result
}

/** 常规查询转换为 count 查询 */
export function convertToCount(type: 'count' | 'findAll', sql: string): string {
  // 匹配 SELECT * 或 SELECT 列名 的正则表达式
  if (type === 'findAll') {
    return sql.replace(/SELECT(\s+\*|\s+[^,\s]+)/i, `SELECT COUNT(*) AS count`)
  }

  if (type === 'count') {
    // ...
  }

  return sql
}

/** 针对 count group 的转换 */
export function convertToCountGroup(options) {
  const { group } = options

  const transformedOptions: any = {
    ...options,
  }

  if (group) {
    transformedOptions.attributes = options.attributes ? options.attributes : []
    for (const field of group) {
      transformedOptions.attributes.push([`fn("COUNT", col("${field}"))`, field])
    }
    transformedOptions.attributes.push([`fn("COUNT", "*")`, 'count'])
  } else {
    transformedOptions.attributes = [
      [`fn("COUNT", "*")`, 'count']
    ]
  }

  return transformedOptions
}

/** 生成 sql */
export function generateSql(seq: any, tableName: string, findOption: any) {
  const queryInterface = seq.getQueryInterface()
  const queryGenerator = queryInterface.queryGenerator

  return queryGenerator.selectQuery(tableName, findOption)
}

/** 自动增加库到 sql 里 */
export function convertToDatabaseName(query: string, databaseName: string) {
  const tableRegex = /`([^`.]+)`(?!\.)/g
  let isInFromClause = false

  const replacedQuery = query.replace(tableRegex, (match, tableName, offset) => {
    if (!isInFromClause && offset > query.indexOf('FROM') && query.indexOf('FROM') !== -1) {
      isInFromClause = true
      return `\`${databaseName}\`.\`${tableName}\``
    }
    return match
  })

  return replacedQuery
}

/** 尝试解析 json 对象 */
export function tryParseJson(str: string | object) {
  if (!_.isString(str)) return str
  try {
    if (isJson(str)) return JSON.parse(str)
    return JSON.parse(str)
  } catch (err) {
    return str
  }
}

/** 转换 json 字段 */
export function convertToJsonField(fields: string[], data: any | any[]) {

  const toData = item => {
    const obj = {}

    _.keys(item).forEach(key => {
      obj[key] = item[key]
      if (_.includes(fields, key)) {
        obj[key] = tryParseJson(item[key])
      }
    })

    return obj
  }

  if (_.isArray(data)) return _.map(data, toData)
  return toData(data)
}

/** 对结果进行转成 */
export function convertToResult(functionName: string, options: NativeQueryFindOption, data: any[]) {

  let count = 0
  let result = data

  if (functionName === 'count') {
    if (data.length === 1) {
      count = _.get(data, '[0].count', -1)
      return count
    }
    return data
  }

  if (functionName === 'findAndCountAll') {
    result = data[0]
    count = _.get(data[1], '[0].count', 0)
  }

  // 将下划线转成小驼峰
  if (options.underscored === true || (options.underscored as any)?.result === true) {
    result = convertToCamelCase(result) as any
  }

  // 转换 json 字段
  if (!_.isEmpty(options.convertJsonField)) {
    result = convertToJsonField(options?.convertJsonField ?? [], result) as any
  }

  if (/findOne|findByPk/.test(functionName)) {
    result = _.first(result)
  }

  if (functionName === 'findAndCountAll') {
    return { list: result, total: count }
  }

  return result
}

/** 查询数据 */
export async function queryData(sequelize: Sequelize, functionName: string, sql: string) {
  let result: any[] = []
  const res = await sequelize.query(sql, { type: 'SELECT', raw: true })

  result = res

  if (functionName === 'findAndCountAll') {
    const count = await sequelize.query(convertToCount('findAll', sql), { type: 'SELECT', raw: true })

    result = [res, count]
  }

  return result
}

/** 获取注册的参数 */
export function getRegisterArg(args: any[]) {
  let namespace = '$'
  let name = args[0]
  let func = args[1]
  if (args.length >= 3) {
    namespace = args[0] as string
    name = args[1]
    func = args[2]
  }
  return { namespace, name, func }
}

// 函数，接受一个函数作为参数，并返回一个新的函数，用于监控性能
export function monitorPerformance(func: Function, name: string) {
  const performance = perfHooks?.performance

  return async function (...args: any[]) {
    if (!performance) return [{}, await func(...args)]

    const rand = genId(16)
    const start = `${name}:start_${rand}`
    const end = `${name}:end_${rand}`
    const measureName = `${name}:measure_${rand}`

    performance.mark(start)

    const result = await func(...args) // 执行原始函数

    performance.mark(end)
    performance.measure(measureName, start, end)

    const performanceEntries = performance.getEntriesByName(measureName)
    const entry = performanceEntries[performanceEntries.length - 1]
    const info = entry

    // 清除性能标记
    performance.clearMarks(start)
    performance.clearMarks(end)

    return [info, result]
  }
}

/** 数据库类型对应的引号 */
export const QUOTE_DICT = {
  mysql: '`',
  postgres: '"',
  postgresql: '"',
  pg: '"',
  oracle: '"',
  oracledb: '"',
  mssql: '[',
  tindex: '`',
  mindex: '`',
  uindex: '`',
  hive: '`',
  doris: '`',
  starrocks: '`'
} as const

/**
 * 为字段名加上数据库类型对应的引号，支持多级字段（如a.b.c -> `a`.`b`.`c`）。
 * 幂等：已加过引号的字段不会重复加。仅支持常规多级字段，特殊表达式（如JSON路径、函数等）需调用方自行处理。
 */
export function wrapColName(
  dbType: 'mysql' | 'postgres' | 'oracle' | 'mssql' | 'tindex' | (string & {}),
  fieldName: string | undefined
) {
  if (!fieldName) return ''
  const quote = QUOTE_DICT[_.lowerCase(dbType)] || '`'
  const closeQuote = quote === '[' ? ']' : quote
  // 检查某一级是否已被正确的引号包裹
  function isWrapped(part: string) {
    return part.startsWith(quote) && part.endsWith(closeQuote)
  }
  // 按.分割，对每一级加引号（已加的不再加），再拼接
  return fieldName.split('.').map(part => isWrapped(part) ? part : `${quote}${part}${closeQuote}`).join('.')
}

/** 解密请求内容 */
export function decryptData(result: any, enableEncryption: boolean | string) {
  if (enableEncryption === '1' || enableEncryption === true || enableEncryption === 'L1') {
    const res = crypto.T1.decrypt(result)
    return res && _.isString(res) ? JSON.parse(res) : res
  }
  if (enableEncryption === 'L2') return crypto.S1.decrypt(result) // 解压缩
  if (enableEncryption === 'L3') {
    const res = crypto.S2.decrypt(result) // 解密
    return res && _.isString(res) ? JSON.parse(res) : res
  }
  return result
}


/** 加密请求内容 */
export function encryptData(result: any, enableEncryption: boolean | string, sign: string) {
  if (enableEncryption === '1' || enableEncryption === true || enableEncryption === 'L1') return result // 不要加密的
  if (enableEncryption === 'L2') return crypto.S1.encrypt(result) // 压缩
  if (enableEncryption === 'L3') return crypto.S2.encrypt(result, sign) // 加密
  return result
}
