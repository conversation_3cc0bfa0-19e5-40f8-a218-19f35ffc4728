import _ from 'lodash'
import type { CloudFunction } from './type'
import * as cookieKit from 'cookie'
import type { Redis } from 'ioredis'

/** 从 redis 里面获取用户信息 */
export const getPermissionDataFromRedis = async (redis: any, sessionId: string) => {
  try {
    const redisRes = await redis.pipeline().select(0).get(sessionId).exec()
    const userStr = _.get(redisRes, [1, 1], '') as string
    const obj = userStr ? JSON.parse(userStr as string) : {}
    return obj || {}
  } catch (err: any) {
    console.error('解析权限错误：', err.message)
    return {}
  }
}

// 查找权限对象
const findPermission = (data: Record<string, any>, key: string | string[]) => {
  let dict: Record<string, any> = {}
  let isLike = false
  // 支持模糊匹配，例如 abiProject*
  if (_.isString(key) && key.indexOf('*') > -1) {
    const prefix = _.split(key, '*')[0]
    isLike = true
    key = prefix
  }

  if (_.isArray(key)) {
    if (isLike) {
      _.forEach(key, k => {
        dict = {
          ...dict,
          ..._.pickBy(data, (_v, p) => _.startsWith(p, k)),
        }
      })
    }

    _.forEach(key, k => {
      dict[k] = data?.[k]
    })

    return dict
  }
  else {
    if (isLike) {
      dict = {
        ...dict,
        ..._.pickBy(data, (_v, p) => _.startsWith(p, key as string))
      }
    } else {
      dict[key] = data?.[key]
    }
    return dict
  }
}

/**
 * 获取用户信息
 * @param headers
 * @param session
 * @param redis
 * @returns
 */
export const getUserRedisInfo = async (headers: Record<string, any>, session?: Record<string, any>, redis?: Redis) => {
  // isSuperAdmin, sessionId, type: userType 这三个有可能为 undefined 需要去 redis 拿
  let user = _.get(session, 'user')
  let sessionId = _.get(user, 'sessionId')
  let isSuperAdmin = false
  let dataPermission = {}

  if (!user || !sessionId) {
    const cookieObj = (!_.isEmpty(headers?.cookie) && cookieKit.parse(headers?.cookie)) || null
    sessionId = _.get(cookieObj, 'sugo.sess') || _.get(cookieObj, 'sugo:sess')
  }

  if (sessionId) {
    const res = await getPermissionDataFromRedis(redis, sessionId)
    user = res.user
    dataPermission = res.dataPermission
    isSuperAdmin = res.user?.type === 'built-in'
  }

  return {
    ...user,
    isSuperAdmin,
    dataPermission
  }
}


type Params = {
  type?: string | string[]
}

/** 获取用户权限配置，返回的字典 key 是驼峰 */
export const getDataPermission = async (params: Params, headers: Record<string, any>, session?: Record<string, any>, redis?: Redis) => {
  const { type } = params

  // isSuperAdmin, sessionId, type: userType 这三个有可能为 undefined 需要去 redis 拿
  // let { isSuperAdmin, sessionId, type: userType } = _.get(session, 'user', {}) || {}
  // let user: Record<string, any> | null = null

  if (!redis) {
    throw new Error('云函数未初始化 Redis 实例，请检查')
  }

  const user = await getUserRedisInfo(headers, session, redis)
  const isSuperAdmin = _.get(session, 'user.isSuperAdmin') || _.get(user, 'isSuperAdmin')
  const userType = _.get(user, 'type')

  let dataPermission = _.get(session, 'user.dataPermission') || _.get(user, 'dataPermission')

  // 上层的 session 没有时才去 redis 里面取
  // if (!dataPermission) {

  //   // 尝试从 cookie 解析 sessionId
  //   if (!sessionId) {
  //     const cookieObj = (!_.isEmpty(headers?.cookie) && cookieKit.parse(headers?.cookie)) || null
  //     sessionId = _.get(cookieObj, 'sugo.sess') || _.get(cookieObj, 'sugo:sess')
  //   }

  //   // 有 sessionId 时，可能这个 sessionId 已经过期了，从 redis 拿不到用户信息了
  //   if (sessionId) {
  //     const res = await getPermissionDataFromRedis(redis, sessionId)
  //     user = res.user
  //     userType = res.user?.type
  //     isSuperAdmin = res.user?.type === 'built-in'
  //     dataPermission = res?.dataPermission
  //   } else {
  //     // console.error('查找不到 sessionId，可能已经登录失效')
  //   }
  // }

  const isAdmin = user?.type === 'built-in' || isSuperAdmin || userType === 'built-in'
  dataPermission = _.mapKeys(dataPermission, (_v, k) => _.camelCase(k))

  if (type) return {
    isAdmin,
    // accessibleUserIds: dataPermission.accessibleUserIds,
    ...findPermission(dataPermission, type || '')
  }

  return {
    isAdmin,
    // accessibleUserIds: dataPermission.accessibleUserIds,
    ...dataPermission
  }
}

/** 获取权限的云函数 */
export const getDataPermissionFn: CloudFunction = async (request, context) => {
  return getDataPermission(request.params, request.headers, request.session, context.redis)
}

/** dataPermission 代理对象 */
export const dataPermissionProxy = (headers: Record<string, any>, session: Record<string, any>, redis: Redis) => {
  let data: null | Record<string, any> = null // 初始数据为 null，表示未加载
  let loadingPromise: Promise<any> | null = null // 用于存储加载数据的 Promise

  // 异步加载数据的函数
  const load = async () => {
    data = await getDataPermission({}, headers, session, redis) || {}
  }

  const getResult = ($data: any, key: string | string[]) => {
    const dict = findPermission($data, key)
    if (_.isString(key) && key.indexOf('*') === -1) return dict[key]
    if (_.isArray(key)) return _.map(key, k => dict[k])
    return dict
  }

  // 创建代理对象
  const proxyObj = {
    get: async (key: string | string[]) => {
      if (data) return getResult(data, key)

      if (!loadingPromise) await load()

      const keys = _.keys(data)
      const noMatchKey = _.difference(_.flatMap([key]), keys)
      if (noMatchKey.filter(i => i).length > 0) {
        console.warn('[dataPermission] 获取的 key 不存在: ', noMatchKey)
      }

      if (data) return getResult(data, key)
    },
    getKeys: async () => {
      if (!loadingPromise) await load()
      return _.keys(data || {})
    },
    getAll: async () => {
      if (!loadingPromise) await load()
      return data || {}
    }
  }

  return proxyObj
}
