const Utils = require('sequelize/lib/utils')

import _ from 'lodash'
import { Op, literal } from 'sequelize'
import type { WhereOptions } from 'sequelize'

import { wrapColName } from './utils'

/**
 * 生成 model 的查询 sql （不会执行查询）
 * @parms model 当前表的模型实例
 * @options {object} 查询参数
 */
export function generateModelQuerySQL(model: any, options: any) {
  return (function (options) {
    const tableNames = {}
    // 使用 model.getTableName 来替代 this.getTableName
    tableNames[model.getTableName(options)] = true

    options = Utils.cloneDeep(options)
    options = Object.assign({}, options, {
      hooks: true,
      rejectOnEmpty: true,
      type: 'SELECT',
      model: model,  // 将 this 替换为 model
      tableNames: Object.keys(tableNames)
    })

    // 使用 model._injectDependentVirtualAttributes 来替代 this._injectDependentVirtualAttributes
    options.originalAttributes = model._injectDependentVirtualAttributes(options.attributes)

    if (options.include) {
      options.hasJoin = true

      // 使用 model._validateIncludedElements 来替代 this._validateIncludedElements
      model._validateIncludedElements(options, tableNames)

      if (
        options.attributes
        && !options.raw
        && model.primaryKeyAttribute  // 使用 model.primaryKeyAttribute 来替代 this.primaryKeyAttribute
        && !options.attributes.includes(model.primaryKeyAttribute)
        && (!options.group || !options.hasSingleAssociation || options.hasMultiAssociation)
      ) {
        options.attributes = [model.primaryKeyAttribute].concat(options.attributes)
      }
    }

    if (!options.attributes) {
      options.attributes = Object.keys(model.rawAttributes)  // 使用 model.rawAttributes 来替代 this.rawAttributes
      options.originalAttributes = model._injectDependentVirtualAttributes(options.attributes)
    }

    model.options.whereCollection = options.where || null

    // 使用 model 来替代 this
    Utils.mapFinderOptions(options, model)

    options = model._paranoidClause(model, options)  // 使用 model._paranoidClause 来替代 this._paranoidClause

    // 获取 queryGenerator
    const queryGenerator = model.sequelize.getQueryInterface().queryGenerator;
    return queryGenerator.selectQuery(model.getTableName(options), options)
  }).call(model, options)
}

/** 生成权限查询的 sql */
export const generateDataPermissionWhere = (where: WhereOptions | any, params: {
  userId: string,
  roleIds?: string[],
  orgIds?: string[],
  /** 数据库类型，默认 mysql */
  dbType?: string,
  /** 当前查询表的模型名称（类名），不是表名，例如 Theme */
  modelName: string
  /** 主应用的权限表，类似 sugo-astro.sugo_data_permission，不要加 ``，内部会处理 */
  permissionTableName: string
  /** 判断权限资源的类型 */
  type: string
  /** 判断有管理全部的类型 */
  adminType?: string
  /** 关联字段，默认 id，如 id=target_id */
  targetFieldName?: string
  /** 创建人字段，默认值：created_by，默认查询创建人的，设置为 null 可屏蔽 */
  createdByFieldName?: string | null
}) => {
  const { userId, orgIds, roleIds, dbType = 'mysql' } = params
  const config = params

  const arrToArgs = (arr?: string[]) => _.join(_.map(arr, i => `'${i}'`), ', ')
  const wrap = (str: string) => wrapColName(dbType, str)

  const bindFieldName = config.targetFieldName || 'id'
  const type = config.type
  const adminType = config.adminType || `${type}_admin`
  const createdByFieldName = _.isNil(config.createdByFieldName) ? 'created_by' : config.createdByFieldName  // createdBy

  // 主表
  const T = wrap(config.modelName || '')
  // 权限表
  const permTableName = wrap(config.permissionTableName)

  const createdByCol = `${T}.${createdByFieldName}`
  const idCol = `${T}.${bindFieldName}`

  const conditions: string[] = [`${wrap('Q.user_id')} = ${arrToArgs([userId])}`]

  if (!_.isEmpty(roleIds)) conditions.push(`${wrap('Q.role_id')} IN(${arrToArgs(roleIds)})`)
  if (!_.isEmpty(orgIds)) conditions.push(`${wrap('Q.institution_id')} IN(${arrToArgs(orgIds)})`)

  // 3. 构建 where 条件
  const dataPermissionWhereSql = `(
    ${!createdByFieldName ? '' : `
    -- 创建人的条件
    ${wrap(createdByCol)} = '${userId}' OR
    `}
    -- 判断是否有“管理”全部的权限
    EXISTS (
      SELECT 1 FROM ${permTableName} as ${wrap('Q')}
      WHERE ${wrap('Q.type')} = '${_.snakeCase(adminType)}' AND ${wrap('Q.status')} = 'on' AND (${conditions.join(' OR ')})
    ) OR
    -- 判断关联的权限资源 id 是否存在
    ${wrap(idCol)} IN(
      SELECT ${wrap('Q.target_id')} FROM ${permTableName} as ${wrap('Q')}
      WHERE ${wrap('Q.type')} = '${_.snakeCase(type)}' AND ${wrap('Q.status')} = 'on' AND (${conditions.join(' OR ')})
    )
  )`

  // (
  //   -- 创建人的条件
  //   `Theme`.`created_by` = '3KHz88OCm' OR
  //   -- 判断是否有“管理”全部的权限
  //   EXISTS (
  //     SELECT 1 FROM `prod219-sugo-astro`.`sugo_data_permission` as Q
  //     WHERE Q.type = 'abi_theme_analysis_admin' AND Q.status = 'on' AND (Q.user_id = '3KHz88OCm' OR Q.role_id IN('iW80FBYwS'))
  //   ) OR
  //   -- 判断关联的权限资源 id 是否存在
  //   `Theme`.`id` IN(
  //     SELECT Q.target_id FROM `prod219-sugo-astro`.`sugo_data_permission` as Q
  //     WHERE Q.type = 'abi_theme_analysis' AND Q.status = 'on' AND (Q.user_id = '3KHz88OCm' OR Q.role_id IN('iW80FBYwS'))
  //   )
  // )

  const _where = where || {}
  // 4. 用 and 的方式合并到 where 里面
  const mergedWhere: any = {
    [Op.and]: [
      _where, // 原来的在前面，确保短路运算
      literal(dataPermissionWhereSql) // 合并新的权限子查询条件
    ]
  }

  return mergedWhere as WhereOptions | any
}
