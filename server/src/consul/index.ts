import { extend } from 'umi-request'
import { join } from 'path'
import _ from 'lodash'
import lzString from 'lz-string'

import type { MicroFrontendAppKeys, Params, Config } from './type'

const request = extend({
  timeout: 1000 * 60 * 10 // 10 分钟超时
})

const configMap: Partial<Config> = {
  request
}

/**
 * 执行子应用的接口
 *
 * 挂到 /app/call-app-services
 * @param ctx
 * @param ref
 */
export async function koaExecuteAppService(ctx: any, ref: any) {
  const { appName, path, params = {} } = ctx.request.body || {}
  const { redisGet, redisSetExpire, getUserForSessionStorage, EXPIRE_TIME, microFrontendApps } = ref

  const userId = params.sugoLogin
  const isAutoLogin = !!params.sugoLogin

  // 自动登录
  if (isAutoLogin) {
    // 获取通过 username 自动登录的标识
    const sessionId = `sg.${lzString.compressToEncodedURIComponent(userId)}` // ctx.session.externalKey
    const currLoginUser = await redisGet(sessionId)
    if (!currLoginUser) {
      const sessData = await getUserForSessionStorage(userId, userId)
      if (sessData) {
        // 手动设置到redis，方便子应用直接通过cookie获取，不使用ctx.session.user=赋值，避免renew产生多个无用记录
        await redisSetExpire(sessionId, Math.floor(EXPIRE_TIME), sessData)
      }
    }
    const cookie = `sugo.sess=${sessionId};`
    _.set(params, 'headers.cookie', cookie)
  }

  const host = microFrontendApps[appName]
  const url = join(host, path).replace(':/', '://')

  const res = await configMap.request?.(url, {
    ...params,
    method: params.method || 'GET',
  })

  ctx.body = res
}

/**
 * 初始化服务
 * @param config
 */
export function initConfig(config: Config) {
  _.forEach(config, (c, k) => {
    configMap[k] = c
  })
}

/**
 * 执行子应用的服务
 * @param appName
 * @param path
 * @param params
 *
 * @example
 * await callAppServices('sugo-total-mut', '/api/xxx', { method: 'GET' })
 */
export async function callAppServices(appName: MicroFrontendAppKeys, path: string, params: Params, config?: Config) {
  const { sugoAuth, sugoLogin } = params
  if (sugoAuth) _.set(params, 'headers.cookie', sugoAuth)
  if (sugoLogin) _.set(params, 'headers["x-sugo-auto-login"]', sugoLogin)

  _.set(params, 'headers["micro-apps"]', appName)

  const host = configMap.mainAppHost || config?.mainAppHost || ''

  if (!host) {
    throw new Error('[callAppServices] 未配置主应用服务地址，请设置 initConfig({ mainAppHost })')
  }

  const req = configMap.request || config?.request

  const url = join(host, path).replace(':/', '://')
  const res = await req?.(url, {
    method: params.method || 'GET',
    requestType: 'json',
    data: {
      appName,
      path,
      params
    }
  })

  return res
}

export function createRequestMothod(appName: MicroFrontendAppKeys) {
  return {
    get: (path: string, params?: Params, config?: Config) => callAppServices(appName, path, { ...params, method: 'GET' }, config),
    post: (path: string, params?: Params, config?: Config) => callAppServices(appName, path, { ...params, method: 'POST' }, config),
    put: (path: string, params?: Params, config?: Config) => callAppServices(appName, path, { ...params, method: 'PUT' }, config),
    del: (path: string, params?: Params, config?: Config) => callAppServices(appName, path, { ...params, method: 'DELETE' }, config),
    patch: (path: string, params?: Params, config?: Config) => callAppServices(appName, path, { ...params, method: 'PATCH' }, config),
  }
}

/**
 * 服务调用类
 *
 * @example
 * 第一个参数是 请求路径
 * 第二个参数是 umiRequest 的配置（可 JSON 序列化的，不支持函数）
 *
 * await AppService.sugoTotalMut.post('/xxxx', { data: { ... } })
 */
export class AppService {
  static callAppServices = callAppServices
  static sugoTotalMut = createRequestMothod('sugo-total-mut')
  static sugoAbi = createRequestMothod('sugo-abi')
  static sugoMonitorAlarms = createRequestMothod('sugo-monitor-alarms')
  static sugoStatDocs = createRequestMothod('sugo-stat-docs')
  static sugoDatasourceManager = createRequestMothod('sugo-datasource-manager')
  static sugoDataCrown = createRequestMothod('sugo-data-crown')
  static sugoAiManagement = createRequestMothod('sugo-ai-management')
  static sugoDataService = createRequestMothod('sugo-data-service')
  static sugoDataForm = createRequestMothod('sugo-data-form')
}
