import { RequestOptionsInit, RequestMethod } from 'umi-request'

export type MicroFrontendAppKeys =
'sugo-total-mut' | 'sugo-abi' | 'sugo-monitor-alarms' | 'sugo-stat-docs' |
'sugo-datasource-manager' | 'sugo-data-crown' | 'sugo-ai-management' |
'sugo-data-service' | 'sugo-data-form' |
(string & {})

/**
 * {
 *  'sugo-data-service': 'http://127.0.0.1:4001',
 *  'sugo-total-mut': 'http://127.0.0.1:4002',
 * }
 */
export type MicroFrontendApps = {
  [key in MicroFrontendAppKeys]: string
}

export type Params = RequestOptionsInit & {
  /** 提供 string 时，指定用户名登录，有 cookie 时就不要设置这里了 */
  sugoLogin?: string
  /** 提供 cookie，会指定写到 headers 里面，用了 sugoLogin 时不要用这个 */
  sugoAuth?: string
}

export type Config = {
  /** 主应用服务地址 */
  mainAppHost: string
  request?: RequestMethod

  [key: string]: any
}
