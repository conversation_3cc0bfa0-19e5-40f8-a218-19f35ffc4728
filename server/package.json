{"name": "sugo-sequelize-cloud-server", "version": "2.13.3", "description": "- 完备的 Typescript 支持，让你轻松借助类型智能来编写代码；\r - 去 controller、service、api 化，不需要关心这些，没有复杂的概念；\r - 只需要定义表结构，一切可在前端通过函数式调用；\r - 支持请求鉴权与加密；\r - 支持 json 字段级的查询；", "main": "dist/cjs/index.js", "types": "dist/cjs/index.d.ts", "scripts": {"start": "father dev", "dev": "father dev", "build": "rm -rf ./dist && rm -rf node_modules/.cache/father/tsc && father build", "build:deps": "father prebundle", "prepublishOnly": "father doctor && npm run build"}, "keywords": [], "authors": [], "license": "MIT", "files": ["dist", "compiled"], "publishConfig": {"access": "public"}, "devDependencies": {"@types/ioredis": "^5.0.0", "@types/lodash": "^4.14.194", "@types/sequelize": "^4.28.15", "father": "^4.3.1", "nanoid": "^3.3.4", "sequelize": "^6.32.0"}, "author": "lizhooh <<EMAIL>>", "dependencies": {"@babel/runtime": "^7.22.15", "cookie": "^0.5.0", "is-json": "^2.0.1", "lodash": "^4.17.21", "umi-request": "^1.4.0"}}