import { Controller, Inject, Post } from '@midwayjs/core'
import { Context } from '@midwayjs/koa'
import { InjectDataSource } from '@midwayjs/sequelize'
import { Sequelize } from 'sequelize-typescript'
import { CloudServer } from 'sugo-sequelize-cloud/server'

import { registerFunction } from '@/clouds/register'
import { ENTITY_MAP } from '@/entity'
import { genId } from '@/utils'

const isDev = process.env.NODE_ENV !== 'production'

@Controller('/')
export class APIController {

  private cloudServer: CloudServer<typeof ENTITY_MAP>

  @Inject()
  ctx: Context

  // 注入默认数据源
  @InjectDataSource()
  defaultDataSource: Sequelize;

  @Post('/api/cloud')
  async cloud() {
    this.cloudServer = new CloudServer({
      entityMap: ENTITY_MAP,
      generateId: genId,
      isDev,
      sequelize: this.defaultDataSource,
      registerFunction,
      logging: isDev
    })
    this.ctx.body = await this.cloudServer.execKoa({
      body: this.ctx.request.body || {},
      headers: this.ctx.headers,
      session: this.ctx.session,
      userId: null
    })
  }
}
