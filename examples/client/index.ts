import { message } from 'antd'
import _ from 'lodash'
import nprogress from 'nprogress'
import { createCloudService } from 'sugo-sequelize-cloud/client'
import { extend } from 'umi-request'

import type { Entity } from './type'

/** 云服务 */
export const Service = createCloudService<Entity>({
  microApp: 'sugo-abi-report-hub',
  baseUrl: '/api/cloud',
  umiRequest: extend({
    headers: {
      'x-master-jwt': window.sessionStorage.getItem('masterJWT') || ''
    }
  }),
  entityMap: {
    Demo: 'Demo',
    Report: 'Report',
    Tag: 'Tag',
    Template: 'Template',
    TemplateCategory: 'TemplateCategory'
  } as any,
  onEvent: (name, { error, data }) => {
    if (name === 'start') {
      if (window.isDev) {
        console.log(`[Cloud] ${data.modelName}.${data.functionName}`, data.params, data.data)
      }
      nprogress.start()
    }
    if (name === 'finish') nprogress.done()
    if (name === 'error') {
      message.error(error.data?.message || error.message)
    }
  }
})

/**
 * 示例： Service.Person.findAll({ where: { name: 'abc' } })
 */
export default Service
