import _ from 'lodash'
import { RequestOptionsInit } from 'umi-request'
import { extend } from 'umi-request'

import type { MicroFrontendAppKeys } from './type'

type Params = RequestOptionsInit

const request = extend({
  timeout: 1000 * 60 * 10 // 10 分钟超时
})

/**
 * 执行子应用的服务
 * @param appName
 * @param path
 * @param params
 *
 * @example
 * callAppServices('sugo-total-mut', '/api/xxx', { method: 'GET' })
 */
export async function callAppServices(appName: MicroFrontendAppKeys, path: string, params: any) {
  _.set(params, 'headers["micro-apps"]', appName)

  const url = `${window.origin}${path}`
  const res = await request(url, {
    method: params.method || 'GET',
    requestType: 'json',
    data: params
  })

  return res
}

export function createRequestMothod(appName: MicroFrontendAppKeys) {
  return {
    get: (path: string, params?: Params) => callAppServices(appName, path, { ...params, method: 'GET' }),
    post: (path: string, params?: Params) => callAppServices(appName, path, { ...params, method: 'POST' }),
    put: (path: string, params?: Params) => callAppServices(appName, path, { ...params, method: 'PUT' }),
    del: (path: string, params?: Params) => callAppServices(appName, path, { ...params, method: 'DELETE' }),
    patch: (path: string, params?: Params) => callAppServices(appName, path, { ...params, method: 'PATCH' }),
  }
}

/**
 * 服务调用类
 *
 * @example
 * 第一个参数是 请求路径
 * 第二个参数是 umiRequest 的配置
 *
 * await AppService.sugoTotalMut.post('/xxxx', { data: { ... } })
 */
export class AppService {
  static callAppServices = callAppServices

  static sugoMainApp = createRequestMothod(' ')
  static sugoTotalMut = createRequestMothod('sugo-total-mut')
  static sugoAbi = createRequestMothod('sugo-abi')
  static sugoMonitorAlarms = createRequestMothod('sugo-monitor-alarms')
  static sugoStatDocs = createRequestMothod('sugo-stat-docs')
  static sugoDatasourceManager = createRequestMothod('sugo-datasource-manager')
  static sugoDataCrown = createRequestMothod('sugo-data-crown')
  static sugoAiManagement = createRequestMothod('sugo-ai-management')
  static sugoDataService = createRequestMothod('sugo-data-service')
  static sugoDataForm = createRequestMothod('sugo-data-form')
}
