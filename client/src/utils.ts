import lz from 'lz-string'
import crypto from './encrypt'

import type { BaseServiceOptions, CloudApiParams } from './type'
import _ from 'lodash'

import { setCache, getCache } from './cache'

import { customAlphabet } from 'nanoid'

// TODO: 10 位数
export const nanoid = customAlphabet('1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 10)
export const genId = (n?: number) => nanoid(n || 10)

const compressTo = (str: string) => lz.compressToEncodedURIComponent(str)

// 取消请求的缓存
export const cancelRequest = (id: RegExp | string | Function, cancelTokenMap: Record<string, AbortController>) => {

  if (_.isFunction(id)) {
    for (const key in cancelTokenMap) {
      if (id(key)) { // 匹配到
        cancelTokenMap[key]?.abort()
        delete cancelTokenMap[key] // 取消后删除
      }
    }
  }
  else if (_.isRegExp(id)) {
    const keys = _.keys(cancelTokenMap).filter(k => id.test(k))
    _.forEach(keys, k => {
      cancelTokenMap[k]?.abort()
      delete cancelTokenMap[k] // 取消后删除
    })
  }
  else if (_.isString(id)) {
    cancelTokenMap[id]?.abort()
    delete cancelTokenMap[id]
  }
}

/**
 * 请求云函数，做了一层包装
 */
export async function requestInstance<M>(
  url: string,
  data: CloudApiParams | CloudApiParams[],
  options: BaseServiceOptions<M>
) {
  const _data = _.isFunction(options?.modifyFn) ? options?.modifyFn(data) : data
  let result, error
  let body: any = _data
  let params: CloudApiParams | CloudApiParams[] = _data
  try {
    const time = Date.now().toString()
    if (!options) options = {} as any
    options.onEvent?.('start', { data: params })

    // 不是数组
    if (!Array.isArray(data)) {
      data = limitWarn(data)
      data = replaceInclude(data, options.entityMap)
      body = data
    } else {
      body = data.map(item => {
        item = limitWarn(item)
        item = replaceInclude(item, options.entityMap)
        return item
      })
    }

    // 加密之前的
    params = body

    // 加密之后的
    if (options.enableEncryption) {
      if (!Array.isArray(body)) {
        body = encryptionData(body, options.enableEncryption, time)
      } else {
        body = body.map(item => encryptionData(item, options.enableEncryption, time))
      }
    }

    // const CancelToken = options.umiRequest?.CancelToken
    let signal
    if (options.cancelId && AbortController) {
      const controller = new AbortController()
      options.cancelTokenMap[options.cancelId] = controller
      signal = controller.signal
    }

    const userId = typeof window === 'object' ? _.get(window, 'sugo.user.id', undefined) : undefined

    const headers: Record<string, string> = {
      'micro-apps': options.microApp || '',
      ...options.headers,
      'x-time': time,
      'x-sing': compressTo(`${options.microApp}|${time}`),
      'content-type': 'application/json'
    }

    let missCache = false

    const cCacheTime = (data as CloudApiParams).params?.cCache as number
    const sCacheTime = (data as CloudApiParams).params?.sCache as number

    const urlQueryParams = _.omitBy({
      ...options.query,
      sCache: sCacheTime
    }, _.isUndefined)

    if (userId) headers['u-id'] = userId

    if (options.enableEncryption === true || options.enableEncryption === 'L1') headers['x-encryption'] = 'L1'
    if (options.enableEncryption === 'L2') headers['x-encryption'] = 'L2'
    if (options.enableEncryption === 'L3') headers['x-encryption'] = 'L3'

    // 这里处理 cCache 逻辑
    if (cCacheTime && cCacheTime > 10) {
      const cache = getCache(data, cCacheTime)
      if (cache) {
        // console.log('缓存命中，跳过请求', params)
        result = cache
        missCache = true // 命中了
      }
    }

    if (!missCache) {
      const res = await options.umiRequest(url, {
        method: 'POST',
        body: JSON.stringify(body),
        params: urlQueryParams,
        requestType: 'json',
        responseType: 'json',
        timeout: 1000 * 60 * 5, // 最大 5 分钟
        timeoutMessage: '接口请求超时，请稍后重试',
        signal,
        headers
      })

      if (!res || !res.success) throw new Error(res?.message)
      result = res
      // 重新缓存
      if (cCacheTime && cCacheTime > 10) {
        // console.log('重新缓存', params)
        setCache(data, result)
      }
    }

    options.onEvent?.('success', { result, data: params })
    // 可以二次获取数据
    if (options.onSuccess) {
      const res2 = await options.onSuccess(result)
      if (res2 !== undefined) return res2
    }

    // TODO：需要解密
    return decryptData(result?.result, options.enableEncryption)

  } catch (err: any) {
    error = err
    if (err.type === 'HttpError' && err.data) {
      options.onEvent?.('error', { error: new Error(err.data.message), data: params })
    } else {
      options.onEvent?.('error', { error: err, data: params })
    }
    throw err
  } finally {
    options.onEvent?.('finish', { error, result, data: params })
  }
}

/**
 * limit 警告
 */
export function limitWarn(data: CloudApiParams) {
  if (typeof data.params?.limit === 'number' && data.params?.limit > 1000 && data.namespace !== '$cloud') {
    console.error(`[Cloud](${data.modelName}.${data.functionName}) limit 限制最大 1000，请精细化粒度或分批处理`)
  }
  return data
}

/**
 * 替换 include
 */
export function replaceInclude(data: CloudApiParams, entityMap: Record<string, string>) {
  if (data.params?.include) {
    data.params = {
      ...data.params,
      include: replaceIncludeModelName(data.params.include, entityMap || {})
    }
  }
  return data
}

/** 加密请求内容 */
export function encryptionData(data: CloudApiParams, enableEncryption: BaseServiceOptions<any>['enableEncryption'], time: number | string) {
  const encrypt = (obj: any) => {
    if (enableEncryption === true || enableEncryption === 'L1') return crypto.T1.encrypt(JSON.stringify(obj))
    if (enableEncryption === 'L2') return crypto.S1.encrypt(obj)
    if (enableEncryption === 'L3') return crypto.S2.encrypt(JSON.stringify(obj), time)
    return obj
  }

  const _params = encrypt(data.params)
  const _data = data.data ? encrypt(data.data) : undefined

  return {
    ...data,
    data: _data,
    params: _params
  } as any as CloudApiParams
}

/** 解密请求内容 */
export function decryptData(result: any, enableEncryption: BaseServiceOptions<any>['enableEncryption']) {
  if (enableEncryption === true || enableEncryption === 'L1') return result // 不要解密的
  if (enableEncryption === 'L2') return crypto.S1.decrypt(result) // 解压缩
  if (enableEncryption === 'L3') return crypto.S2.decrypt(result) // 解密
  return result
}

/**
 * 替换 include 的 model name
 * @param include - 要替换的 include
 * @param modelMap - 包含模型映射的对象
 * @returns 替换后的 include
 */
export function replaceIncludeModelName(include: any, modelMap: Record<string, string>): any {
  if (typeof include === 'string') return modelMap[include] || include
  if (Array.isArray(include)) {
    return include.map((item: any) => {
      const newItem = replaceIncludeModelName(item, modelMap)
      if (item?.include) newItem.include = replaceIncludeModelName(item.include, modelMap)
      return newItem
    })
  }
  if (typeof include === 'object') return {
    ...(include as Record<string, any>), // 使用类型断言指定 include 为 Record<string, any>
    model: modelMap[include.model] || include,
  }
}


/**
 * 判断空对象
 */
export function isEmptyObject(obj: any) {
  if (!obj) return true
  return Object.keys(obj).length === 0
}

