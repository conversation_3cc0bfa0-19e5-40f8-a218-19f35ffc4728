import * as D from './encrypt'
import _ from 'lodash'

const cache: Record<string, {
  time: number,
  value: any
}> = {}

if (typeof window === 'object') {
  (window as any).__CLOUD_CLIENT_CACHE__ = cache

  _.set(window, '__CLOUD_CLIENT_CACHE__.d', D)
}

export const setCache = (params: any, value: any) => {
  const key = JSON.stringify(params)
  const data = {
    value,
    time: Date.now()
  }
  cache[key] = data
}

export const delCache = (params: any) => {
  const key = JSON.stringify(params)
  delete cache[key]
}

export const delAllCache = () => {
  const keys = Object.keys(cache)
  keys.forEach(k => {
    delete cache[k]
  })
}

export const getCache = (params: any, time: number) => {
  const key = JSON.stringify(params)
  const data = cache[key]
  if (!data || (Date.now() - (time + data.time)) > 0) {
    delete cache[key]
    return null
  }
  return data.value
}
