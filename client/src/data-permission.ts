import { createCloudService } from './'
import _ from 'lodash'

const ADMIN_TYPE_MAP = {
  /** 指标 */
  INDICES: 'indices_admin',
  /** 数据源 */
  CONNECT: 'connect_admin',
  /** 模型 */
  MODEL: 'model_admin',
  /** 指标体系 */
  INDICES_ANALYSIS: 'analysis_indice_admin',
  /** 主题分析 */
  DASHBOARDS: 'dashboards_admin',
  /** 数据应用 */
  ABI_PROJECT: 'abi_project_admin',
  /** 我的分析 */
  ABI_THEME_ANALYSIS: 'abi_theme_analysis_admin'
}

const MANAGE = 'manage'

export interface Options {
  umiRequest: any
  /** 获取用户信息 */
  getUser: () => {
    id: string
    type: string
    SugoRoles: any[]
    [key: string]: any
  }
  /** 拿管理的类型 */
  adminTypeList: string[]
}

type ItemType = {
  id: string
  targetId: string
  roleId: string
  userId: string
  config: any
  createdBy?: string | null
  status: 'on' | 'off'
  [key: string]: any
}

/**
 * 创建资源权限查询服务
 *
 * @deprecated 已经弃用，请使用其他方案，
 *
 * 请使用 Cloud.$fn.getDataPermission({ type: 'abiThemeAnalysis*' }) 获取权限配置
 */
export const createDataPermissionService = (options: Options) => {
  const { umiRequest, getUser, adminTypeList } = options

  /** 主应用云服务 */
  const mainCloud = createCloudService({
    microApp: ' ',
    baseUrl: '/app/main/cloud',
    umiRequest,
    enableEncryption: false,
    entityMap: {
      // 前端与表类名映射
      DataPermission: 'SugoDataPermission'
    }
  })

  const dataPermissionCacheMap: Record<string, ItemType> = {}
  const loadedMap = {} as Record<string, boolean>
  let isFirstLoad = false

  const userId = () => {
    const uid = getUser()?.id as string
    if (!uid) {
      console.warn('[loadAdminDataPermissions] 用户 id 不存在，请检查')
    }
    return uid
  }
  const roleIds = () => getUser()?.SugoRoles?.map((i: any) => i.id) as string[]
  const isSuperAdmin = () => getUser()?.type === 'built-in'

  const findDataPermission = async (where: any) => {
    // 授予下级组织的权限，上级自动拥有
    const { userIds: teamUserIds, roleIds: teamRoleIds, institutionIds } = getUser()?.accessibilityByInstitution || {}

    // 获取权限设置（当前用户能看见的资源）
    const res: any[] = await mainCloud.DataPermission.findAll({
      where: {
        ...where,
        $or: [
          { user_id: [userId(), ...(teamUserIds || [])] },
          { role_id: [...(roleIds() || []), ...(teamRoleIds || [])] },
          { institution_id: institutionIds || [] }
        ]
      },
      attributes: {
        exclude: ['created_at', 'created_by', 'updated_at', 'updated_by']
      }
    })

    return _.map(res, i => _.mapKeys(i, (_v, k) => _.camelCase(k))) as ItemType[]
  }

  /** 加载超管项，只加载一次 */
  const loadAdminDataPermissions = async () => {
    const list = await findDataPermission({
      type: _.isEmpty(adminTypeList) ? { $like: '%_admin' } : { $in: adminTypeList }
    })

    _.forEach(list, item => {
      const prev = dataPermissionCacheMap[item.targetId]
      dataPermissionCacheMap[item.targetId] = {
        ...(item as any),
        status: prev?.status === 'on' || item.status === 'on' ? 'on' : 'off' // 有一个就为 on
      }
    })

    isFirstLoad = true
  }

  /**
   * 获取资源权限数据
   * 如果有管理员权限，就列表里有一个 $admin
   *
   * 管理权限，可以编辑和删除
   * 使用权限，不能编辑和删除
   *
   * 绿色是使用，管理是全部
   *
   * @example
   * id=['*'], type='indice_type' 时加载所有 id
   */
  const loadDataPermissions = async (ids: string[], type?: string) => {
    if (!userId()) return []
    if (!isFirstLoad) await loadAdminDataPermissions()

    const isAll = ids?.[0] === '*'
    if (isAll && type && loadedMap[type]) return

    const list = await findDataPermission({
      target_id: isAll ? undefined : { $in: ids },
      type: type ? type : undefined
    })

    // 全部的加载完成
    if (isAll && type) loadedMap[type] = true

    _.forEach(list, item => {
      const prev = dataPermissionCacheMap[item.targetId]
      dataPermissionCacheMap[item.targetId] = {
        ...(item as any),
        config: _.merge(prev?.config, item?.config, {}) // 这里 targetId 不唯一，要合并 config
      }
    })

    // console.log('loadDataPermissions', dataPermissionCacheMap)

    return _.pick(dataPermissionCacheMap, ids)
  }

  const createCommonManageFn = (isAdminFn: () => boolean) => {
    return (id: string, createdBy?: string) => {
      const dict = dataPermissionCacheMap
      const { config } = dict[id] || {}
      if (isSuperAdmin()) return true
      if (isAdminFn()) return true

      if (userId() && createdBy === userId()) return true
      if (config?.auth === MANAGE) return true
      return false
    }
  }

  /** 获取权限缓存的字典 */
  const getDataPermissionCacheMap = () => dataPermissionCacheMap

  /** 是否是指标管理权限 */
  const isIndiceAdmin = () => isSuperAdmin() || dataPermissionCacheMap[ADMIN_TYPE_MAP.INDICES]?.status === 'on'

  /** 是否是指标分析管理权限 */
  const isIndiceAnalysisAdmin = () =>
    isSuperAdmin() || dataPermissionCacheMap[ADMIN_TYPE_MAP.INDICES_ANALYSIS]?.status === 'on'

  /** 是否是主题分析管理权限  */
  const isDashboardsAdmin = () => isSuperAdmin() || dataPermissionCacheMap[ADMIN_TYPE_MAP.DASHBOARDS]?.status === 'on'

  /** 是否是 abi 项目管理权限 */
  const isAbiProjectAdmin = () => isSuperAdmin() || dataPermissionCacheMap[ADMIN_TYPE_MAP.ABI_PROJECT]?.status === 'on'

  /** 是否是 abi 主题分析管理 */
  const isAbiThemeAnalysisAdmin = () =>
    isSuperAdmin() || dataPermissionCacheMap[ADMIN_TYPE_MAP.ABI_THEME_ANALYSIS]?.status === 'on'

  /** 是否是数据源管理权限 */
  const isDatasourceAdmin = () => isSuperAdmin() || dataPermissionCacheMap[ADMIN_TYPE_MAP.CONNECT]?.status === 'on'

  /** 指标口径是否有管理权限 */
  const allowIndiceSpecManageById = (indiceId: string, specIdOrVersionId: string, createdBy?: string) => {
    const dict = dataPermissionCacheMap
    const { config } = dict[indiceId] || {}
    if (isSuperAdmin()) return true
    if (isIndiceAdmin()) return true
    if (specIdOrVersionId === 'default_spec') return true
    if (userId() && createdBy === userId()) return true

    // true 为选中的
    const has = _.findKey(config?.specSelectedMap || {}, (v, k) => k?.indexOf(specIdOrVersionId) > -1 && v === true)

    if (has && config?.specMap?.[specIdOrVersionId] === MANAGE) return true
    if (has && config?.versionMap?.[specIdOrVersionId] === MANAGE) return true
    return false
  }

  /** 指标口径界面 item 是否可见 */
  const allowViewSpecById = (indiceId: string, specId: string, createdBy?: string) => {
    const dict = dataPermissionCacheMap
    const { config } = dict[indiceId] || {}
    if (isSuperAdmin()) return true
    if (isIndiceAdmin()) return true

    // 默认口径都有！！
    // if (specId === 'default_spec') return true
    if (userId() && createdBy === userId()) return true

    // specSelectedMap 里面存的可能是 specId,versionId
    // true 为选中的
    const has = _.findKey(config?.specSelectedMap || {}, (v, k) => k?.indexOf(specId) > -1 && v === true)
    if (has) return true

    return false
  }

  /** 检测指标是否有管理权限 */
  const allowIndiceManageById = (indiceId: string, createdBy?: string) => {
    const dict = dataPermissionCacheMap
    const { config } = dict[indiceId] || {}
    if (isSuperAdmin()) return true
    if (dict.indices_admin?.status === 'on') return true

    if (userId() && createdBy === userId()) return true

    // 1. specSelectedMap 至少有一个
    // 2. versionMap 至少有一个 manage
    const has = _.find(config?.specSelectedMap, (i, k) => {
      if (i === true) {
        const ids = (k || '').split(',') || []
        return ids.find(id => config?.versionMap?.[id] === MANAGE || config?.specMap?.[id] === MANAGE)
      }
      return false
    })

    if (has) return true

    return false
  }

  /** 检测指标是否有权限（不非管理还是使用） */
  const allowIndiceById = (indiceId: string, createdBy?: string) => {
    const dict = dataPermissionCacheMap
    const { config } = dict[indiceId] || {}
    if (isSuperAdmin()) return true
    if (dict.indices_admin?.status === 'on') return true
    if (userId() && createdBy === userId()) return true

    const has = config?.target_id === indiceId
    if (has) return true

    return false
  }

  /** 检测指标体系是否有管理权限 */
  const allowIndiceAnalysisManageById = createCommonManageFn(isIndiceAnalysisAdmin)

  /** 是否允许管理主题分析 */
  const allowDashboardsManageById = createCommonManageFn(isDashboardsAdmin)

  /** 允许管理 abi 项目 */
  const allowAbiProjectManageById = createCommonManageFn(isAbiProjectAdmin)

  /** 允许管理 abi 主题分析 */
  const allowAbiThemeAnalysisManageById = createCommonManageFn(isAbiThemeAnalysisAdmin)

  /** 允许管理数据源 */
  const allowDatasourceManageById = createCommonManageFn(isDatasourceAdmin)

  /** 获取 abi 项目的  */
  const getAbiProjectIds = () => {
    const items = _.filter(dataPermissionCacheMap, i => i.type === 'abi_project')
    return _.uniq(items.map(i => i.targetId))
  }

  /** 获取 abi 项目的  */
  const getAbiThemeAnalysisIds = () => {
    const items = _.filter(dataPermissionCacheMap, i => i.type === 'abi_theme_analysis')
    return _.uniq(items.map(i => i.targetId))
  }

  return {
    createCommonManageFn,
    loadDataPermissions,
    allowIndiceSpecManageById,
    allowViewSpecById,
    allowDashboardsManageById,
    allowIndiceManageById,
    allowIndiceAnalysisManageById,
    allowAbiThemeAnalysisManageById,
    allowIndiceById,
    allowAbiProjectManageById,
    allowDatasourceManageById,
    getDataPermissionCacheMap,
    isIndiceAdmin,
    isIndiceAnalysisAdmin,
    isAbiProjectAdmin,
    isDashboardsAdmin,
    isDatasourceAdmin,
    isAbiThemeAnalysisAdmin,
    getAbiProjectIds,
    getAbiThemeAnalysisIds
  }
}
