/** 查询条件 */
export type WhereType<M = any> =
  | {
      [K in keyof M]?:
        | M[K]
        | null
        | {
            $eq?: M[K] | null
            $ne?: M[K] | null
            $not?: M[K] | null | string | number
            $gt?: M[K] | Date
            $gte?: M[K] | Date
            $lt?: M[K] | Date
            $lte?: M[K] | Date
            $like?: string | null
            $in?: (M[K] | (string & {}))[]
            $notIn?: (M[K] | (string & {}))[]
            $between?: [M[K], M[K]]
            $notBetween?: [M[K], M[K]]
            $regexp?: M[K] | null
            $notRegexp?: M[K] | null
            // 以下为 json 数组操作符
            $contains?: (string | number)[] | Record<string, any>
            $overlap?: (string | number)[]
            $contained?: (string | number)[]
            $adjacent?: (string | number)[]
            $strictLeft?: (string | number)[]
            $noExtendRight?: (string | number)[]
            $noExtendLeft?: (string | number)[]
          }
        | {
            $and?: WhereType<M>[]
            $or?: WhereType<M>[]
          }
    }
  | {
      $and?: WhereType<M>[]
      $or?: WhereType<M>[]
    }
  | string
  | {
      [key: string]: any
    }

export type Relation = {
  tableName: string // 类似 SugoUser
  /** 关联字段名称，默认 id */
  relatedName?: string
  fieldMapping: {
    /** 新字段名称 */
    [key: string]: {
      /** 原字段名称 */
      sourceName: string
      /** 类似 select */
      attributes?: (string | [string, string])[] // 类似 attributes: ['id', 'username']
    }
  }
}

export type LikeAttributes<M> =
  | (keyof M)[]
  | string[][]
  | (string | string[])[]
  | {
      exclude?: (keyof M)[] | string[] | string[][]
      include?: (keyof M)[] | string[] | string[][]
    }
  | {
      [key in Exclude<keyof M, 'exclude' | 'include'>]: string
    }

/** 关联查询 */
export type LikeInclude<M> = {
  /** 关联的模型 */
  model: string
  /** 别名，这个要跟模型的属性名称一致，不然会报错，一般不用设置 */
  as?: string
  where?: WhereType<M>
  attributes?: LikeAttributes<M>
  limit?: number
  /** 指定的关联查询是否必须，默认为 true，关联不了将不查询出来 */
  required?: boolean
  include?: LikeInclude<M>[],
  /** 设置为 false 时即可查询假删除的 */
  paranoid?: boolean
  /** 作用域 */
  scopes?: Record<string, any>
}

/** 云函数参数 */
export interface CloudApiParams<M = any> {
  functionName: string
  /** $ 是云函数标识 */
  modelName: '$' | (string & {})
  namespace?: '$cloud' | '$redis' | '$use' | (string & {})
  params: {
    /** 传给后端的缓存参数，支持数字（单位毫秒）或字符串，例如 P1D */
    sCache?: number | string
    /** 传给前端的缓存参数，在内存里缓存，支持数字（单位毫秒），最低是 10ms */
    cCache?: number

    /** 过滤条件 */
    where?: WhereType<M>
    /** 查询数量，单次查询最大 1000 */
    limit?: number
    /** 偏移量，大于 0 */
    offset?: number
    /** 排序 */
    order?:
      | [keyof M, 'DESC' | 'ASC'][]
      | {
          [key in keyof M]?: 'DESC' | 'ASC'
        }
      | [string, 'DESC' | 'ASC'][]
      | {
          [key: string]: 'DESC' | 'ASC'
        }
    /**
     * 根据主键 ID 去重，COUNT(DISTINCT(id))
     */
    distinct?: boolean
    /** 选择字段，或排除字段 */
    attributes?: LikeAttributes<M>
    /**
     * @deprecated 警告此参数将抛弃，请用 group 字段
     */
    groupBy?: (keyof M)[] | string[]
    /** 分组聚合 */
    group?: (keyof M)[] | string[]
    /** 分组后二次筛选 */
    having?: WhereType<M> | Record<string, any>
    /** 关联查询 include 语法 */
    include?: string | LikeInclude<any> | LikeInclude<any>[] | string[]
    /**
     * 主应用/其他应用关联表查询
     *
     * @example
     * relations: {
     *   tableName: 'SugoUser', // '#App' # 开头的是本地库
     *   fieldMapping: {
     *     createdBy: {
     *       fieldName: 'createdUser',
     *       attributes: ['id', 'username', 'first_name']
     *     }
     *   }
     * }
     */
    relations?: Relation | Relation[]
    /** 是否查询已经删除的数据（默认假删除），设置为 false 表示查询已经删除的 */
    paranoid?: boolean
    /** 取消查询 id */
    cancelId?: string
    /** 创建后是否不返回 */
    returning?: boolean
    /**
     * 使用权限，开启后，会经过权限的过滤（后端需要配置映射表）
     *
     * - usePermission: 'new' // 新的方式，通过 mysql 关联表方式
     * 下面写法将来会弃用：
     * - usePermission: true // 后端有配置映射表，查询 redis
     * - usePermission: 'abiThemeAnalysis' // 后端没有配置的情况
     */
    usePermission?: boolean | 'new' | (string & {})

    /** 请求的头参数，类似 { cookie: 'xxx' } */
    headers?: Record<string, string> | any

    [key: string]: any
  }
  /** update/create/batchcreate 时才有 */
  data?: any | any[] | undefined
  /**
   * 跨表查询配置，目前应该使用不到
   * @experimental 实验性
   */
  config?: {
    dbName?: string
    tableName?: string
    mapDbName?: string
  }
}

/** 查询选项 */
export type FindOption<M> = NonNullable<CloudApiParams<M>['params']>

/** 查询所有选项 */
export type AllFindOption<M> = FindOption<M>

/** upsert 专用选项类型 */
export type UpsertOptions<T, M = any> = {
  /** 是否返回更新后的记录 */
  returning?: boolean
  /** 用于确定记录是否存在的字段，可以是模型中的任何字段 */
  fields?: (keyof T)[] | string[]
  /** 是否执行验证 */
  validate?: boolean
  /** 是否触发钩子函数 */
  hooks?: boolean
  /** where 条件 */
  where?: WhereType<M>
  /** 其他选项 */
  headers?: any
  cancelId?: string
  usePermission?: boolean | string
}

/** 原生查询选项 */
export type NativeQueryFindOption<M> = FindOption<M> & {
  onlyGenerateSql?: boolean
  /** 是否转换 json 字段，将 json 字符串转成 json 对象，填写字段名称*/
  convertJsonField?: string[]
  /** 是否自动转移字段名称，例如将结果的 user_name 转成 userName，默认开启 */
  underscored?:
    | boolean
    | {
        /** 是否换查询参数，例如 userName 转成 user_name */
        options?: boolean
        /** 是否转换结果集，例如 user_name 转成 userName */
        result?: boolean
      }
}

export type BaseApiOptions = {
  modelName: string
  onAddBatchTask?: (api: CloudApiParams) => any
  cancelId?: string
  cancelTokenMap: Record<string, AbortController>
  /** url query */
  query?: Record<string, any>
  /** 请求头 */
  headers?: Record<string, any> | any
  onSuccess?: (result: any) => any | undefined | Promise<any>
}

/** 事件处理函数类型 */
export type OnEventFn = (
  eventName: 'error' | 'success' | 'start' | 'finish',
  eventData: {
    /** 请求错误 */
    error?: any
    /** 请求结果 */
    result?: any
    /** 请求参数 */
    data?: CloudApiParams | CloudApiParams[]
  }
) => any

export type BaseServiceFnOptions<Entity> = {
  microApp?: string
  umiRequest: any
  baseUrl: string
  /** 是否启用请求加密 */
  enableEncryption?: boolean | 'L1' | 'L2' | 'L3'
  entityMap: {
    [key in keyof Entity]: string
  }
  cancelTokenMap?: Record<string, AbortController>
  /** 用于修饰参数用的 */
  modifyFn?: (params: CloudApiParams | CloudApiParams[]) => CloudApiParams | CloudApiParams[]
  onEvent?: OnEventFn
}

export type BaseServiceOptions<M> = BaseApiOptions & BaseServiceFnOptions<M>
export type NativeDbQueryServiceOptions<M> = BaseApiOptions &
  BaseServiceFnOptions<M> & {
    config: CloudApiParams<any>['config']
  }

export type ObjectAndNull<T> = {
  [K in keyof T]: T[K] | null | string
}

export type BulkCreateOpts<M = any> = {
  validate?: boolean
  /** 指定需要插入的字段，默认为全部插入 */
  fields?: (keyof Omit<M, 'id'>)[]
  /** 是否忽略重复的数据 */
  ignoreDuplicates?: (keyof Omit<M, 'id'>)[] | false
  /** 发生重复数据时是否执行更新操作 */
  updateOnDuplicate?: (keyof Omit<M, 'id'>)[]
  /** 是否返回插入的数据 */
  returning?: boolean
}

export type LikeEntity = {
  id: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
  [key: string]: any
}

// 将函数结果改成 promise
export type PromiseResult<A> = {
  [K in keyof A]: A[K] extends (...args: infer P) => infer R
    ? (...args: P) => Promise<R> // 将 A[K] 的返回类型 R 转换为 Promise<R>
    : A[K]
}

export type FnOpts = {
  /** 取消 id */
  cancelId?: string
  /** 查询参数 */
  query?: Record<string, any>
  /** 请求头 */
  headers?: any
  /** 返回数据时，可以对数据进行修改 */
  onSuccess?: (result: any) => any | undefined | Promise<any>
}

/**
 * 基础模型接口,定义了与数据库交互的通用方法
 * @template M - 模型的类型,默认为 LikeEntity
 */
export interface BaseModel<M = LikeEntity> {
  /** 服务选项 */
  opts: BaseServiceOptions<M>

  /** 模型名称 */
  modelName: string

  /**
   * 发送请求到服务器
   * @param api - API 参数,不包含 modelName
   * @param opt - 可选的请求选项
   * @returns 请求的响应
   */
  $request(
    api: Omit<CloudApiParams<M>, 'modelName'>,
    opt?: { cancelId?: string; query?: Record<string, any>; headers: any }
  ): Promise<any>

  /**
   * 查询所有符合条件的记录
   * @param options - 查询选项
   * @returns 符合条件的记录数组
   */
  findAll(options: AllFindOption<M>): Promise<M extends string ? Record<string, any>[] : M[]>

  /**
   * 查询符合条件的记录并返回总数
   * @param options - 查询选项
   * @returns 包含记录列表和总数的对象
   */
  findAndCountAll(
    options: FindOption<M>
  ): Promise<{ list: M extends string ? Record<string, any>[] : M[]; total: number }>

  /**
   * 查询符合条件的单个记录
   * @param options - 查询选项
   * @returns 符合条件的单个记录
   */
  findOne(options: FindOption<M>): Promise<M extends string ? Record<string, any> : M>

  /**
   * 查找记录,如果不存在则创建
   * @param options - 查询和创建选项
   * @returns 找到或创建的记录,以及是否为新创建的布尔值
   */
  findOrCreate(
    options: FindOption<M> & { defaults: Omit<M, 'id'> | Record<string, any> }
  ): Promise<[M extends string ? Record<string, any> : M, boolean]>

  /**
   * 根据主键查询记录
   * @param id - 记录的主键
   * @param options - 可选的查询选项
   * @returns 对应主键的记录
   */
  findByPk(id: string, options?: FindOption<M>): Promise<M extends string ? Record<string, any> : M>

  /**
   * 创建新记录
   * @param value - 要创建的记录数据
   * @param options - 创建选项
   * @returns 创建的记录
   */
  create(
    value: ({ id?: string } & Omit<M, 'id'>) | Record<string, any>,
    options?: Pick<FindOption<M>, 'headers' | 'cancelId' | 'usePermission' | 'returning'>
  ): Promise<M extends string ? Record<string, any> : M>

  /**
   * 更新符合条件的记录
   * @param value - 要更新的字段和值
   * @param options - 更新选项
   * @returns 更新的记录数量
   */
  update(
    value: M extends string ? Record<string, any> : Partial<ObjectAndNull<Omit<M, 'id'>>>,
    options: FindOption<M> & { returning?: boolean; individualHooks?: boolean }
  ): Promise<[number]>

  /**
   * 根据主键更新记录
   * @param id - 记录的主键
   * @param value - 要更新的字段和值
   * @param options - 更新选项
   * @returns 更新的记录数量
   */
  updateByPk(
    id: string,
    value: M extends string ? Record<string, any> : Partial<ObjectAndNull<Omit<M, 'id'>>>,
    options?: {
      returning?: boolean
      individualHooks?: boolean
      where?: FindOption<M>['where']
    } & Pick<FindOption<M>, 'headers' | 'cancelId' | 'usePermission'>
  ): Promise<[number]>

  /**
   * 创建或更新记录
   * @param value - 要创建或更新的记录数据
   * @param options - 操作选项
   * @returns 创建或更新的记录,以及是否为新创建的布尔值
   */
  upsert<T extends Partial<M extends string ? Record<string, any> : M>>(
    value: T,
    options?: UpsertOptions<T, M>
  ): Promise<[T, boolean]>

  /**
   * 计算符合条件的记录数量
   * @param options - 查询选项
   * @returns 记录数量
   */
  count<T = number>(options: FindOption<M>): Promise<T>

  /**
   * 批量创建记录
   * @param records - 要创建的记录数组
   * @param options - 批量创建选项
   * @returns 创建的记录数组
   */
  bulkCreate(
    records: Partial<ObjectAndNull<M extends string ? Record<string, any> : M>>[],
    options?: BulkCreateOpts
  ): Promise<Partial<M extends string ? Record<string, any> : M>[]>

  /**
   * 删除符合条件的记录
   * @param options - 删除选项
   * @returns 删除的记录数量
   */
  delete(options: { where: FindOption<M>['where']; limit?: number; force?: boolean }): Promise<number>

  /**
   * 根据主键删除记录
   * @param id - 记录的主键
   * @param options - 删除选项
   * @returns 删除的记录数量
   */
  deleteByPk(id: string, options?: { force?: boolean; where?: FindOption<M>['where'] }): Promise<number>

  createdAt?: string
  createdBy?: string
  updatedAt?: string
  updatedBy?: string
}

/**
 * 云服务实例类型
 */
export type CloudService<Entity = any, Func = any, BaseRedis = any> = {
  [K in keyof Entity]: BaseModel<Entity[K]>
} & {
  /**
   * 执行云函数
   * @deprecated 已弃用,请使用新版本（v2.5.5+）的 $fn.xxx(params, options?)
   */
  $execute: <K extends keyof Func>(
    functionName: K | (string & {}),
    params?: Func[K] extends (...args: any) => any ? Parameters<Func[K]>[0] : Func[K],
    opt?: FnOpts
  ) => Promise<any>

  /** 批量执行云函数 */
  $batch: (
    action: ($service: {
      [key in keyof Entity]: BaseModel<Entity[key]>
    }) => any[]
  ) => Promise<any>

  /** 执行云函数 */
  $fn: PromiseResult<Func>

  /** 查询 Redis,如果服务端支持 */
  $redis: BaseRedis

  /** 使用跨库查询 */
  $use: <T = any>(config: string) => NativeDbQuery<T>

  /** 取消请求，支持正则查询，或回调查询匹配 */
  $cancel: (id: string | RegExp | ((id: string) => boolean)) => any

  /** 请求云函数 */
  $cloud: BaseModel<any>
}

export type MicroFrontendAppKeys =
'sugo-total-mut' | 'sugo-abi' | 'sugo-monitor-alarms' | 'sugo-stat-docs' |
'sugo-datasource-manager' | 'sugo-data-crown' | 'sugo-ai-management' |
'sugo-data-service' | 'sugo-data-form' |
(string & {})

/**
 * {
 *  'sugo-data-service': 'http://127.0.0.1:4001',
 *  'sugo-total-mut': 'http://127.0.0.1:4002',
 * }
 */
export type MicroFrontendApps = {
  [key in MicroFrontendAppKeys]: string
}
