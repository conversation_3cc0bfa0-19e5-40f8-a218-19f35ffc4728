import { isEmptyObject, requestInstance, cancelRequest } from './utils'
import _ from 'lodash'

import type {
  AllFindOption,
  BaseServiceFnOptions,
  NativeDbQueryServiceOptions,
  CloudApiParams,
  FindOption,
  BaseServiceOptions,
  NativeQueryFindOption,
  ObjectAndNull,
  BulkCreateOpts,
  LikeEntity,
  PromiseResult,
  FnOpts,
  CloudService,
  BaseModel,
  UpsertOptions
} from './type'

/**
 * @deprecated 已弃用，请使用 Cloud.$fn.getDataPermission({ type: 'abi*' })
 */
export { createDataPermissionService } from './data-permission'
export { AppService } from './consul'

export type { CloudApiParams, BaseModel, CloudService } from './type'
export { genId } from './utils'

/** 基础 API 类 */
export class BaseApi<M = LikeEntity> implements BaseModel<M> {
  public opts: BaseServiceOptions<M>

  constructor(options: BaseServiceOptions<M>) {
    this.opts = options
  }

  public get modelName() {
    return this.opts.modelName
  }

  /** 公共请求 */
  public async $request(
    api: Omit<CloudApiParams<M>, 'modelName'>,
    opt?: { cancelId?: string; query?: Record<string, any>; headers: any }
  ): Promise<any> {
    const url = `${this.opts.baseUrl}?${this.modelName}.${api.functionName}`
    const data = { ...api, modelName: this.modelName }

    // 这里触发批量的逻辑，暂存到一个数组里
    if (this.opts.onAddBatchTask !== undefined) {
      this.opts.onAddBatchTask(data)
      return
    }
    const cancelId = api.params?.cancelId || opt?.cancelId || undefined
    const headers = api.params?.headers || opt?.headers || undefined
    return requestInstance(url, data, {
      ...this.opts,
      ...opt,
      headers,
      cancelId
    })
  }

  /** 查询列表 */
  public async findAll(options: AllFindOption<M>): Promise<M extends string ? Record<string, any>[] : M[]> {
    return this.$request({ functionName: 'findAll', params: options })
  }

  /** 查询列表并返回总数 */
  public async findAndCountAll(
    options: FindOption<M>
  ): Promise<{ list: M extends string ? Record<string, any>[] : M[]; total: number }> {
    return this.$request({ functionName: 'findAndCountAll', params: options })
  }

  /** 查询一个 */
  public async findOne(options: FindOption<M>): Promise<M extends string ? Record<string, any> : M> {
    return this.$request({ functionName: 'findOne', params: options })
  }

  /** 查询或创建 */
  public async findOrCreate(
    options: FindOption<M> & { defaults: Omit<M, 'id'> }
  ): Promise<[M extends string ? Record<string, any> : M, boolean]> {
    return this.$request({ functionName: 'findOrCreate', params: options })
  }

  /** 根据 id 查询 */
  public async findByPk(id: string, options?: FindOption<M>): Promise<M extends string ? Record<string, any> : M> {
    return this.$request({
      functionName: 'findByPk',
      params: { ...options, where: { ...(options?.where as any), id } },
      data: id
    })
  }

  /** 创建 */
  public async create(
    value: { id?: string } & Omit<M, 'id'>,
    options?: Pick<FindOption<M>, 'headers' | 'cancelId' | 'usePermission' | 'returning'>
  ): Promise<M extends string ? Record<string, any> : M> {
    return this.$request({
      functionName: 'create',
      data: value,
      params: options || {}
    })
  }

  /** 更新，返回改变的行数 */
  public async update(
    value: M extends string ? Record<string, any> : Partial<ObjectAndNull<Omit<M, 'id'>>>,
    options: FindOption<M> & {
      returning?: boolean
      individualHooks?: boolean
    }
  ): Promise<[number]> {
    if (isEmptyObject(options.where)) {
      this.opts.onEvent?.('error', {
        error: new Error('update where cannot be empty')
      })
      throw new Error('update where cannot be empty')
    }
    return this.$request({
      functionName: 'update',
      params: options,
      data: value
    })
  }

  /** 更新，返回改变的行数 */
  public async updateByPk(
    id: string,
    value: M extends string ? Record<string, any> : Partial<ObjectAndNull<Omit<M, 'id'>>>,
    options?: {
      returning?: boolean
      individualHooks?: boolean
      where?: FindOption<M>['where']
    } & Pick<FindOption<M>, 'headers' | 'cancelId' | 'usePermission'>
  ): Promise<[number]> {
    if (!id) {
      throw new Error('updateByPk id cannot be empty')
    }
    return this.$request({
      functionName: 'update',
      params: { ...options, where: { ...(options?.where as any), id } } as any,
      data: value
    })
  }

  /** 创建或保存（未测试） */
  public async upsert<T extends Partial<M extends string ? Record<string, any> : M>>(
    value: T,
    options?: UpsertOptions<T, M>
  ): Promise<[T, boolean]> {
    return this.$request({
      functionName: 'upsert',
      params: options || {},
      data: value
    })
  }

  /** 返回个数 */
  public async count<T = number>(options: FindOption<M>): Promise<T> {
    return this.$request({ functionName: 'count', params: options })
  }

  /** 批量更新 */
  public async bulkCreate(
    records: Partial<ObjectAndNull<M extends string ? Record<string, any> : M>>[],
    options?: BulkCreateOpts
  ): Promise<Partial<M extends string ? Record<string, any> : M>[]> {
    return this.$request({
      functionName: 'bulkCreate',
      params: (options as any) || {},
      data: records
    })
  }

  /** 删除 */
  public async delete(options: { where: FindOption<M>['where']; limit?: number; force?: boolean }): Promise<number> {
    if (isEmptyObject(options.where)) {
      this.opts.onEvent?.('error', {
        error: new Error('delete where cannot be empty')
      })
      throw new Error('delete where cannot be empty')
    }
    return this.$request({ functionName: 'destroy', params: options })
  }

  /** 删除 */
  public async deleteByPk(id: string, options?: { force?: boolean; where?: FindOption<M>['where'] }): Promise<number> {
    if (!id) {
      throw new Error('deleteByPk id cannot be empty')
    }
    return this.$request({
      functionName: 'destroy',
      params: { ...options, where: { ...(options?.where as any), id } },
      data: id
    })
  }

  // /** 执行云函数 */
  // public async $execute<T = any>(functionName: string, params: T) {
  //   return this.$request({
  //     functionName,
  //     params: params as any,
  //     namespace: this.modelName
  //   })
  // }
}

/** redis 查询类 */
export class BaseRedis {
  constructor(options: BaseServiceOptions<any>) {
    this.opts = options
  }

  get modelName() {
    return 'redis'
  }

  public opts: BaseServiceOptions<any>
  /** 公共请求 */
  public async $request(api: Omit<CloudApiParams<any>, 'modelName' | 'params'>): Promise<any> {
    const url = `${this.opts.baseUrl}?${this.modelName}.${api.functionName}`
    const data = {
      params: {},
      ...api,
      modelName: this.modelName,
      namespace: '$redis'
    }

    return requestInstance(url, data, this.opts)
  }

  /** 获取批量的 key 值 */
  public keys(pattern: string): Promise<string[]> {
    return this.$request({ functionName: 'keys', data: [pattern] })
  }

  /** SCAN 命令用于迭代遍历 Redis 数据库中的键 */
  public scan(cursor: number | string, count?: number | string): Promise<[string, string[]]> {
    return this.$request({
      functionName: 'scan',
      data: count !== undefined ? [cursor, 'COUNT', count] : [cursor]
    })
  }

  /** 获取指定 key 的 value */
  public get(key: string): Promise<string | null> {
    return this.$request({ functionName: 'get', data: [key] })
  }

  /** 设置指定 key 的 value */
  public set(
    key: string,
    value: any,
    token?: 'EX' | 'PX' | 'XX' | 'NX' | 'EXAT' | 'PXAT',
    time?: number
  ): Promise<'Ok' | null> {
    return this.$request({
      functionName: 'set',
      data: [key, value, token, time]
    })
  }

  /** 删除指定 key */
  public del(key: string | string[]): Promise<any> {
    return this.$request({ functionName: 'del', data: [key] })
  }

  /** 批量获取 keys */
  public mget(keys: string[]): Promise<string[]> {
    return this.$request({ functionName: 'mget', data: [keys] })
  }

  /** 批量设置 keys-value */
  public mset(data: Record<string, string>): Promise<'Ok' | null> {
    return this.$request({ functionName: 'mset', data: [data] })
  }

  /** 删除指定哈希表中的一个或多个字段 */
  public hdel(data: Record<string, string>): Promise<'Ok' | null> {
    return this.$request({ functionName: 'hdel', data: [data] })
  }

  /** 获取指定哈希表中的一个字段的值 */
  public hget(key: string, field: string): Promise<string | null> {
    return this.$request({ functionName: 'hget', data: [key, field] })
  }

  /** 设置指定哈希表中一个字段的值 */
  public hset(key: string, data: Record<string, string>): Promise<'Ok' | null> {
    return this.$request({ functionName: 'hset', data: [key, data] })
  }

  /** 获取指定哈希表中一个或多个字段的值 */
  public hmget(key: string, fields: string[]): Promise<(string | null)[]> {
    return this.$request({ functionName: 'hmget', data: [key, fields] })
  }

  /** 获取指定哈希表中一个或多个字段的值 */
  public hmset(key: string, data: Record<string, string>): Promise<(string | null)[]> {
    return this.$request({ functionName: 'hmset', data: [key, data] })
  }

  /** 获取指定哈希表中所有字段和值 */
  public hgetall(key: string): Promise<Record<string, any>> {
    return this.$request({ functionName: 'hgetall', data: [key] })
  }

  /** 获取指定哈希表中所有字段名称 */
  public hkeys(key: string): Promise<string[]> {
    return this.$request({ functionName: 'hkeys', data: [key] })
  }

  /** 获取指定哈希表中字段的数量 */
  public hlen(key: string): Promise<number> {
    return this.$request({ functionName: 'hlen', data: [key] })
  }

  /** 获取指定哈希表中所有字段的值 */
  public hvals(key: string): Promise<string[]> {
    return this.$request({ functionName: 'hvals', data: [key] })
  }

  /** 判断指定哈希表中是否存在指定字段 */
  public hexists(key: string): Promise<boolean> {
    return this.$request({ functionName: 'hexists', data: [key] })
  }

  /** 获取指定哈希表中指定字段的值的字节数 */
  public hstrlen(key: string, field: string): Promise<number> {
    return this.$request({ functionName: 'hstrlen', data: [key, field] })
  }

  /** 仅当指定哈希表中不存在指定字段时，设置指定哈希表中一个字段的值 */
  public hsetnx(key: string, field: string): Promise<number> {
    return this.$request({ functionName: 'hsetnx', data: [key, field] })
  }

  /** 获取指定列表的索引处元素 */
  public lindex(key: string, index: number): Promise<string> {
    return this.$request({ functionName: 'lindex', data: [key, index] })
  }

  /** 在指定列表中插入元素 */
  public linsert(key: string, direction: 'BEFORE' | 'AFTER', pivot: string, value: any): Promise<string> {
    return this.$request({
      functionName: 'linsert',
      data: [key, direction, pivot, value]
    })
  }

  /** 获取指定列表的长度 */
  public llen(key: string): Promise<number> {
    return this.$request({ functionName: 'llen', data: [key] })
  }

  /** 获取指定列表的一段元素 */
  public lrange(key: string, direction: 'BEFORE' | 'AFTER', pivot: string, value: any): Promise<string> {
    return this.$request({
      functionName: 'lrange',
      data: [key, direction, pivot, value]
    })
  }

  /** 从指定列表中删除匹配的元素 */
  public lrem(key: string, count: number, value: any): Promise<number> {
    return this.$request({ functionName: 'lrem', data: [key, count, value] })
  }

  /** 将指定列表中的索引处元素设置为新值 */
  public lset(key: string, index: number, value: any): Promise<'Ok' | null> {
    return this.$request({ functionName: 'lset', data: [key, index, value] })
  }

  /** 向指定列表的头部插入一个或多个元素 */
  public lpush(key: string, values: any[]): Promise<number> {
    return this.$request({ functionName: 'lpush', data: [key, values] })
  }

  /** 从指定列表的头部删除并返回元素 */
  public lpop(key: string): Promise<string> {
    return this.$request({ functionName: 'lpop', data: [key] })
  }

  /** 将指定列表中的元素移动到另一个列表中 */
  public lmove(
    source: string,
    destination: string,
    whereFrom: 'LEFT' | 'RIGHT',
    whereTo: 'LEFT' | 'RIGHT'
  ): Promise<string> {
    return this.$request({
      functionName: 'lmove',
      data: [source, destination, whereFrom, whereTo]
    })
  }

  /** 从指定列表的尾部删除并返回元素 */
  public rpop(key: string): Promise<string> {
    return this.$request({ functionName: 'rpop', data: [key] })
  }

  /** 向指定列表的尾部插入一个或多个元素 */
  public rpush(key: string, values: any[]): Promise<number> {
    return this.$request({ functionName: 'rpush', data: [key, values] })
  }

  /** 向指定集合添加一个或多个元素 */
  public sadd(key: string, values: any[]): Promise<number> {
    return this.$request({ functionName: 'sadd', data: [key, values] })
  }

  /** 从指定集合中删除一个或多个元素 */
  public srem(key: string, values: any[]): Promise<number> {
    return this.$request({ functionName: 'srem', data: [key, values] })
  }

  /** 从指定集合中删除一个随机元素并返回该元素 */
  public spop(key: string): Promise<number> {
    return this.$request({ functionName: 'spop', data: [key] })
  }

  /** 获取指定集合中元素的数量 */
  public scard(key: string): Promise<number> {
    return this.$request({ functionName: 'scard', data: [key] })
  }

  /** 获取指定集合中所有元素 */
  public smembers(key: string): Promise<string> {
    return this.$request({ functionName: 'smembers', data: [key] })
  }

  /** 获取指定集合与另一个集合之间的差集 */
  public sdiff(keys: string[]): Promise<string[]> {
    return this.$request({ functionName: 'sdiff', data: [keys] })
  }

  /** 获取指定集合与另一个集合之间的交集 */
  public sinter(keys: string[]): Promise<string[]> {
    return this.$request({ functionName: 'sinter', data: [keys] })
  }

  /** 获取指定集合与另一个集合之间的并集 */
  public sunion(keys: string[]): Promise<string[]> {
    return this.$request({ functionName: 'sunion', data: [keys] })
  }

  /** 向指定有序集合添加成员及其分数 */
  public zadd(key: string, values: any[]): Promise<string | number> {
    return this.$request({ functionName: 'zadd', data: [key, values] })
  }

  /** 获取指定有序集合中指定范围内的成员 */
  public zrange(key: string, start: number, stop: number, withScores: 'WITHSCORES'): Promise<string[]> {
    return this.$request({
      functionName: 'zrange',
      data: [key, start, stop, withScores]
    })
  }

  /** 获取指定成员在有序集合中的排名 */
  public zrank(key: string, member: string): Promise<number> {
    return this.$request({ functionName: 'zrank', data: [key, member] })
  }

  /** 获取指定有序集合中的成员数量 */
  public zcard(key: string): Promise<number> {
    return this.$request({ functionName: 'zcard', data: [key] })
  }

  /** 获取指定有序集合中分数在指定范围内的成员数量 */
  public zcount(key: string, min: string | number, max: string | number): Promise<number> {
    return this.$request({ functionName: 'zcount', data: [key, min, max] })
  }

  public incr(key: string): Promise<number> {
    return this.$request({ functionName: 'incr', data: [key] })
  }

  public incrby(key: string, increment: number): Promise<number> {
    return this.$request({ functionName: 'incrby', data: [key, increment] })
  }

  public incrbyfloat(key: string, increment: number): Promise<number> {
    return this.$request({ functionName: 'incrbyfloat', data: [key, increment] })
  }

  public decr(key: string): Promise<number> {
    return this.$request({ functionName: 'decr', data: [key] })
  }

  public decrby(key: string, decrement: number): Promise<number> {
    return this.$request({ functionName: 'decrby', data: [key, decrement] })
  }
}

/** 原生查询类，会转成原生 sql */
export class NativeDbQuery<M> {
  constructor(options: NativeDbQueryServiceOptions<M>) {
    this.opts = options
  }

  public opts: NativeDbQueryServiceOptions<M>
  /** 公共请求 */
  public async $request(api: Omit<CloudApiParams<M>, 'modelName'>): Promise<any> {
    const url = `${this.opts.baseUrl}?native.${api.functionName}`
    const data = {
      ...api,
      modelName: 'native',
      config: this.opts.config,
      namespace: '$use'
    }

    return requestInstance(url, data, this.opts)
  }

  /** 查询列表 */
  public async findAll(options: NativeQueryFindOption<M>): Promise<M[]> {
    return this.$request({ functionName: 'findAll', params: options })
  }

  /** 查询列表并返回总数 */
  public async findAndCountAll(options: NativeQueryFindOption<M>): Promise<{ list: M[]; total: number }> {
    return this.$request({ functionName: 'findAndCountAll', params: options })
  }

  /** 查询一个 */
  public async findOne(options: NativeQueryFindOption<M>): Promise<M> {
    return this.$request({ functionName: 'findOne', params: options })
  }

  /** 根据 id 查询 */
  public async findByPk(id: string, options?: Omit<NativeQueryFindOption<M>, 'where'>): Promise<M> {
    return this.$request({
      functionName: 'findByPk',
      params: options || {},
      data: id
    })
  }

  /** 返回个数 */
  public async count<T = number>(options: NativeQueryFindOption<M>): Promise<T> {
    return this.$request({ functionName: 'count', params: options })
  }
}

/**
 * 创建基础的云服务
 * @param {BaseServiceFnOptions<Entity>} options
 * @returns {CloudService<Entity, Func>}
 */
export const createCloudService = <Entity = any, Func = any>(
  options: Omit<BaseServiceFnOptions<Entity>, 'entityMap'> & {
    entityMap: {
      [key in keyof Entity]: string
    }
  }
): CloudService<Entity, Func, BaseRedis> => {
  // 取消请求
  const cancelTokenMap: Record<string, AbortController> = options.cancelTokenMap || {}

  // 缓存的实例
  const instanceMap: Record<string | '$cloud' | '$execute' | '$redis', any> = {
    $cloud: new BaseApi<any>({ modelName: '$', ...options, cancelTokenMap })
  }

  // 转成一个数组 [[key, value]]
  const mapList = Object.entries(options.entityMap || {}) as [string, string][]

  for (const [key, value] of mapList) {
    instanceMap[key] = new BaseApi({
      modelName: value,
      ...options,
      cancelTokenMap
    })
  }

  /**
   * 执行云
   * @param functionName
   * @param params
   * @param opt query 是后面携带的查询参数
   * @returns
   *
   * @deprecated 已启用，请使用新版本（v2.5.2+）的 $fn.xxx(parmas, options)
   */
  const $execute = async <K extends keyof Func>(
    functionName: K | (string & {}),
    params?: Func[K] extends (...args: any) => any ? Parameters<Func[K]>[0] : Func[K],
    opt?: FnOpts
  ) => {
    return await instanceMap.$cloud.$request(
      {
        functionName,
        params,
        namespace: '$cloud'
      },
      opt
    )
  }

  const $fn: PromiseResult<Func> = new Proxy<any>(
    {},
    {
      get: (_target, key) => {
        return async function (params: any, opt?: FnOpts) {
          const res = await instanceMap.$cloud.$request(
            {
              functionName: _.isSymbol(key) ? key : _.camelCase(key),
              params,
              namespace: '$cloud'
            },
            opt
          )

          if (key === 'getDataPermission' && typeof window !== 'undefined') {
            // 可访问同机构+下级机构的用户的分析
            const accessibleUserIds = _.get(window, 'sugo.user.accessibilityByInstitution.userIds') || []
            const userId = _.get(window, 'sugo.user.id')
            const ids = _.uniq([userId, ...accessibleUserIds])
            _.set(res, 'accessibleUserIds', ids)
            _.set(res, 'userId', userId)
          }

          return res
        }
      }
    }
  )

  const $batch = async (
    action: ($serivce: {
      [key in keyof Entity]: BaseApi<Entity[key]>
    }) => any[]
  ) => {
    const batchCollect: CloudApiParams<any>[] = []
    const _serivce: any = {}

    for (const [key, value] of mapList) {
      _serivce[key] = new BaseApi({
        modelName: value,
        ...options,
        cancelTokenMap,
        onAddBatchTask: (d: any) => batchCollect.push(d)
      })
    }

    await action(_serivce)

    const data = [...batchCollect]
    const url = `${options.baseUrl}?batch=${data.map(i => `${i.modelName}.${i.functionName}`).join(',')}`

    return requestInstance(url, data, options as any)
  }

  const $redis = new BaseRedis({
    modelName: 'redis',
    ...options,
    cancelTokenMap
  })

  instanceMap.$execute = $execute
  instanceMap.$fn = $fn
  instanceMap.$batch = $batch
  instanceMap.$redis = $redis
  instanceMap.$use = (config: string) => {
    const _config: CloudApiParams['config'] = {}
    if (typeof config === 'string') {
      _config.mapDbName = config
    }
    return new NativeDbQuery({
      modelName: '',
      ...options,
      cancelTokenMap,
      config: _config
    })
  }
  instanceMap.$cancel = (id: any) => cancelRequest(id, cancelTokenMap)
  const result = instanceMap as unknown as CloudService<Entity, Func>
  // 类型校验
  return result satisfies CloudService<Entity, Func>
}
