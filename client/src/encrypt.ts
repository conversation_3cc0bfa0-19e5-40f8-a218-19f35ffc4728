// src/encrypt.ts

import lz from 'lz-string'
import _ from 'lodash'

const fromCharCode = (buffer: any) => {
  if (!buffer) return buffer
  // 分块处理避免堆栈溢出
  const chunkSize = 32768
  const chunks: string[] = []
  for (let i = 0; i < buffer.length; i += chunkSize) {
    const chunk = buffer.subarray(i, i + chunkSize)
    chunks.push(String.fromCharCode.apply(null, chunk))
  }
  return chunks.join('')
}

// 加密
export function encrypt(str: string, n: number): string {
  const buffer = new Uint16Array(str.length);
  const direction = Math.abs(n) % 2 === 0 ? -1 : 1
  for (let i = 0; i < str.length; i++) {
    const code = str.charCodeAt(i)
    const shift = direction * (n + i)
    const newCode = (code + shift + 65536 * 2) % 65536
    buffer[i] = newCode
  }
  return fromCharCode(buffer)
}

// 解密
export function decrypt(str: string, n: number): string {
  const buffer = new Uint16Array(str.length);
  const direction = Math.abs(n) % 2 === 0 ? -1 : 1
  for (let i = 0; i < str.length; i++) {
    const code = str.charCodeAt(i)
    const shift = direction * (n + i)
    const originalCode = (code - shift + 65536 * 2) % 65536
    buffer[i] = originalCode
  }
  return fromCharCode(buffer)
}

const getN = (s: string): number => {
  return s.charCodeAt(s.length - 1)
}

export default {
  // T1 只压缩
  T1: {
    encrypt: (str: string) => lz.compressToEncodedURIComponent(str),
    decrypt: (str: string) => lz.decompressFromEncodedURIComponent(str)
  },
  // T2 先压缩后加密
  T2: {
    encrypt: (str: string, sign: string) => {
      str = String(str)
      const n = getN(sign)
      const s = lz.compressToEncodedURIComponent(str) // 压缩
      return `${encrypt(s, n)}#T${sign}` // 加密
    },
    decrypt: (str: string, sign: string) => {
      str = String(str)
      const n = getN(sign)
      const s = decrypt(str.replace(new RegExp(`#T${sign}`), ''), n)
      return lz.decompressFromEncodedURIComponent(s)
    }
  },
  // 只压缩
  S1: {
    encrypt: (obj: any) => {
      const str = JSON.stringify(obj)
      return lz.compressToEncodedURIComponent(str) // 压缩
    },
    decrypt: (str: string) => {
      str = String(str)
      const c = lz.decompressFromEncodedURIComponent(str)
      try {
        return c ? JSON.parse(c) : c
      }
      catch (e) {
        console.error('JSON 解析错误', e)
        return c
      }
    }
  },
  // 针对请求的加解密
  S2: {
    // 加密时，传的是 obj + time
    encrypt: (obj: any, timestamp: number | string) => {
      const str = JSON.stringify(obj)
      const sign = String(timestamp).slice(-4)
      const n = getN(sign)
      const s = lz.compressToEncodedURIComponent(str) // 压缩
      return `${encrypt(s, n)}#T${sign}` // 加密
    },
    // 解密时，从 str 里面提取 sign 即可
    decrypt: (str: string) => {
      str = String(str)
      const arr = str.split('#T')
      const sign = arr[arr.length - 1]
      const n = getN(sign)
      const s = decrypt(str.replace(new RegExp(`#T${sign}`), ''), n)
      const c = lz.decompressFromEncodedURIComponent(s)
      try {
        return c ? JSON.parse(c) : c
      }
      catch (e) {
        console.error('JSON 解析错误', e)
        return c
      }
    }
  }
}
