{"name": "sugo-sequelize-cloud-client", "version": "2.13.3", "description": "- 完备的 Typescript 支持，让你轻松借助类型智能来编写代码；\r - 去 controller、service、api 化，不需要关心这些，没有复杂的概念；\r - 只需要定义表结构，一切可在前端通过函数式调用；\r - 支持请求鉴权与加密；\r - 支持 json 字段级的查询；", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.js", "scripts": {"start": "father dev", "dev": "father dev", "build": "rm -rf ./dist && rm -rf node_modules/.cache/father/tsc && father build", "build:deps": "father prebundle", "prepublishOnly": "father doctor && npm run build"}, "keywords": [], "authors": [], "license": "MIT", "files": ["dist", "compiled"], "publishConfig": {"access": "public"}, "devDependencies": {"father": "^4.3.1", "umi-request": "^1.4.0"}, "author": "lizhooh <<EMAIL>>", "dependencies": {"@types/lz-string": "^1.5.0", "lodash": "^4.17.21", "lz-string": "^1.5.0", "@babel/runtime": "^7.22.15"}}