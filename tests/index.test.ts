import _ from 'lodash'

import { init, funcMap } from './init'
import { createCloudService } from '../client/src'
import { CloudServer } from '../server/src'
import { cloudParamsToNative } from '../server/src/convert'
import { generateDataPermissionWhere } from '../server/src/sql'

describe('函数测试', () => {
  test('findAll()', async () => {
    const { clientCloud } = await init()
    const res = await clientCloud.Tag.findAll({})

    expect(res).toEqual([])
  })

  test('findAndCountAll()', async () => {
    const { clientCloud } = await init()
    const res = await clientCloud.Tag.findAndCountAll({})

    expect(res.total).toEqual(0)
    expect(res.list).toEqual([])
  })

  test('count()', async () => {
    const { clientCloud } = await init()
    const res = await clientCloud.Tag.count({})

    expect(res).toEqual(0)
  })

  test(`count(): group`, async () => {
    const { clientCloud } = await init()
    await clientCloud.Tag.bulkCreate([
      { title: 'a-1', order: 0 },
      { title: 'a-2', order: 2 },
      { title: 'a-3', order: 2 },
      { title: 'a-4', order: 3 }
    ])
    const res = await clientCloud.Tag.count({
      where: { title: { $like: 'a-%' } },
      group: ['order']
    })
    const group = _.keyBy(res as any, 'order')
    expect(group[2]?.count).toEqual(2)
    expect(group[3]?.count).toEqual(1)
  })

  test(`create({ title: '测试' })`, async () => {
    const { clientCloud } = await init()
    const res = await clientCloud.Tag.create({ id: 'aaa', title: '测试' })

    expect(res.id).toEqual('aaa')
    expect(res.title).toEqual('测试')
  })

  test(`update()`, async () => {
    const { clientCloud } = await init()
    await clientCloud.Tag.create({ title: 'update', order: -1 })

    const res = await clientCloud.Tag.update(
      {
        order: "literal('`order` + 1')"
      },
      {
        where: { title: 'update' }
      }
    )
    expect(res[0]).toEqual(1)

    const res2 = await clientCloud.Tag.findOne({
      where: { title: 'update' }
    })
    expect(res2?.order).toEqual(0)
  })

  test(`updateByPk(id)`, async () => {
    const { clientCloud } = await init()
    await clientCloud.Tag.create({ id: 'updateByPk', title: 'aaa' })

    const res = await clientCloud.Tag.updateByPk('updateByPk', {
      title: '666'
    })
    expect(res[0]).toEqual(1)

    const res2 = await clientCloud.Tag.findOne({
      where: { id: 'updateByPk' }
    })
    expect(res2?.title).toEqual('666')
  })

  test(`upsert()`, async () => {
    const { clientCloud } = await init()
    const res = await clientCloud.Tag.upsert({ title: 'a' }, { where: { title: 'a' } })
    expect((res[0] as any).id).not.toBeNull()

    const res2 = await clientCloud.Tag.upsert({ title: 'bbb' }, { where: { id: (res[0] as any).id } })
    expect(res2[0].title).toEqual('bbb')
  })

  test(`delete({ title: { $like: '%te%' }  })`, async () => {
    const { clientCloud } = await init()
    await clientCloud.Tag.create({ title: 'test' })
    const res = await clientCloud.Tag.delete({
      where: { title: { $like: '%te%' } }
    })

    expect(res).toEqual(1)
  })

  test(`deleteByPk(id)`, async () => {
    const { clientCloud } = await init()
    await clientCloud.Tag.create({ title: 'test', id: 'deleteByPk' })
    const res = await clientCloud.Tag.deleteByPk('deleteByPk')

    expect(res).toEqual(1)
  })

  test(`bulkCreate([a, b, c])`, async () => {
    const { clientCloud } = await init()
    const res = await clientCloud.Tag.bulkCreate([{ title: 'a' }, { title: 'b' }, { title: 'c' }])

    expect(res.map(i => i.title)).toEqual(['a', 'b', 'c'])

    const res2 = await clientCloud.Tag.bulkCreate(
      [
        { id: res[0].id, title: 'aa' },
        { id: res[1].id, title: 'ba' },
        { id: res[2].id, title: 'ca' }
      ],
      { updateOnDuplicate: ['title'] }
    )

    expect(res2.map(i => i.title)).toEqual(['aa', 'ba', 'ca'])
  })

  test(`findOrCreate()`, async () => {
    const { clientCloud } = await init()
    const res = await clientCloud.Tag.findOrCreate({
      where: { title: '223' },
      defaults: { title: '223' }
    })
    expect(res[0].title).toEqual('223')
  })

})

describe('操作符测试', () => {
  test('$like', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 't1' }, { title: 't2' }])

    const count = await clientCloud.Tag.count({
      where: { title: { $like: '%t%' } }
    })

    expect(count).toBeGreaterThanOrEqual(2)
  })

  test('$in', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { id: 'a', title: 'aa' },
      { id: 'b', title: 'bb' },
      { id: 'c', title: 'cc' }
    ])

    const res = await clientCloud.Tag.findAll({
      where: { id: { $in: ['a', 'c'] } }
    })

    expect(res.map(i => i.title)).toEqual(['aa', 'cc'])
  })

  test('$gte', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { title: 'aac', order: 12 },
      { title: 'bbc', order: 13 }
    ])

    const count = await clientCloud.Tag.count({
      where: { order: { $gte: 10 } }
    })

    expect(count).toBeGreaterThanOrEqual(2)
  })

  test('$or', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { title: '.js', userIds: ['a', 'b'] },
      { title: '.ts', userIds: ['a', 'c'] }
    ])

    const count = await clientCloud.Tag.count({
      where: {
        $or: [{ title: '.js' }, { title: '.ts' }]
      }
    })
    expect(count).toBeGreaterThanOrEqual(2)
  })

  test('$contains', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'json', config: { name: 'abc' } }])

    const res = await clientCloud.Tag.findOne({
      where: { 'config.name': 'abc' }
    })
    expect(res.title).toEqual('json')

    const res2 = await clientCloud.Tag.findOne({
      where: { config: { $contains: { name: 'abc' } } }
    })
    expect(res2.title).toEqual('json')
  })

  test('attributes.exclude', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'attributes.exclude' }])

    const res = await clientCloud.Tag.findOne({
      where: { title: 'attributes.exclude' },
      attributes: {
        exclude: ['id', 'color', 'config', 'createdAt']
      }
    })

    // 查询出来的不能包含这个
    expect(_.keys(res)).not.toContainEqual(expect.arrayContaining(['id', 'color', 'config', 'createdAt']))
  })
})

describe('聚合查询测试', () => {
  test('COUNT', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { title: 'count', order: 10 },
      { title: 'count', order: 20 }
    ])

    const res = await clientCloud.Tag.count({
      attributes: [["fn('COUNT', '*')", 'count']],
      group: ['title']
    })

    expect(res[0].count).toBeGreaterThanOrEqual(2)
  })

  test('SUM', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { title: 'sum', order: 10 },
      { title: 'sum', order: 20 }
    ])

    const res = await clientCloud.Tag.count({
      where: { title: 'sum' },
      attributes: [['fn("SUM", col("order"))', 'sum']],
      group: ['title']
    })

    expect(res[0].sum).toBeGreaterThanOrEqual(30)
  })

  test('MAX', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'max', order: 99999 }])

    const res = await clientCloud.Tag.count({
      attributes: [["fn('MAX', col('order'))", 'max']],
      group: ['id']
    })

    expect(res[0].max).toEqual(99999)
  })
})

describe('关联查询测试', () => {
  test('include: Tag', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { id: 'r-1', title: 'r1' },
      { id: 'r-2', title: 'r2', parentId: 'r-1' },
      { id: 'r-3', title: 'r3', parentId: 'r-1' },
      { id: 'r-4', title: 'r4', parentId: 'r-2' }
    ])

    const list = await clientCloud.Tag.findAll({
      where: { title: { $like: 'r%' } },
      include: {
        model: 'Tag',
        as: 'parent'
      }
    })

    expect(list.filter(i => !!i.parent).length).toEqual(3)
  })

  test('include: required', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { id: 'rs-1', title: 'rs1' },
      { id: 'rs-2', title: 'rs2', parentId: 'rs-1' },
      { id: 'rs-3', title: 'rs3', parentId: 'rs-1' },
      { id: 'rs-4', title: 'rs4', parentId: 'rs-2' }
    ])

    const list = await clientCloud.Tag.findAll({
      where: { title: { $like: 'r%' } },
      include: {
        model: 'Tag',
        as: 'parent',
        required: true
      }
    })

    expect(list.length).toEqual(3)
  })

  test('include: where', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { id: 'rw-1', title: 'rw1' },
      { id: 'rw-2', title: 'rw2', parentId: 'rw-1' },
      { id: 'rw-3', title: 'rw3', parentId: 'rw-1' },
      { id: 'rw-4', title: 'rw4', parentId: 'rw-2' }
    ])

    const list = await clientCloud.Tag.findAll({
      where: { title: { $like: 'r%' } },
      include: {
        model: 'Tag',
        as: 'parent',
        where: { title: 'rw2' }
      }
    })

    expect(list.length).toEqual(1)
  })
})

describe('批量查询测试', () => {
  test('$batch', async () => {
    const { clientCloud } = await init()
    await clientCloud.Tag.bulkCreate([{ title: 'batch1' }, { title: 'batch2' }, { title: 'batch3' }])
    const [list, total] = await clientCloud.$batch(s => [s.Tag.findAll({}), s.Tag.count({})])
    expect(list.length).toBeGreaterThanOrEqual(3)
    expect(total).toBeGreaterThanOrEqual(3)
  })
})

describe('云函数测试', () => {
  test('register function', async () => {
    const { clientCloud, cloudServer } = await init()

    // 注册
    cloudServer.register('sum', funcMap.sum)

    // 前端调用
    const res = await clientCloud.$fn.sum([1, 2, 3])

    expect(res).toEqual(6)
  })

  test('sequelize transaction', async () => {
    const { clientCloud, cloudServer } = await init()

    cloudServer.register('transaction', funcMap.transaction)

    // 前端调用
    await clientCloud.$execute('transaction', {})
    const res = await clientCloud.Tag.findOne({
      where: { title: 'transaction' }
    })

    expect(res).toEqual(null)
  })

  test('inside $execute（内联使用）', async () => {
    const { clientCloud, cloudServer } = await init()

    cloudServer.register('avg', funcMap.avg)
    cloudServer.register('sum', funcMap.sum)
    cloudServer.register('inside', funcMap.inside)
    cloudServer.register('inside2', funcMap.inside2)

    // 前端调用
    const res = await clientCloud.$execute('inside', [1, 2, 3])

    expect(res.sum).toEqual(6)
    expect(res.sum2).toEqual(6)
    expect(res.avg).toEqual(2)
    expect(res.avg2).toEqual(2)

    const res2 = await clientCloud.$fn.inside2([1, 2, 3])
    expect(res2).toEqual(12)
  })

  test('setting plugin', async () => {
    const { clientCloud, cloudServer } = await init()

    cloudServer.setOptions({
      plugin: {
        hello: () => 1
      }
    })

    cloudServer.register('plugin', funcMap.plugin)

    // 前端调用
    const res = await clientCloud.$execute('plugin', {})

    expect(res).toEqual(1)
  })

  test('主应用 main 云服务注册', async () => {
    const mainService = createCloudService({
      microApp: ' ',
      baseUrl: '/app/main/cloud',
      umiRequest: (url, options) => {
        return { result: [], success: true }
      },
      // 对应主应用里面的 sequelize model 定义
      entityMap: {
        DataPermission: 'DataPermission'
      }
    })
    const res = await mainService.DataPermission.findAll({
      where: { id: 1 }
    })
    expect(res).toEqual([])
  })
})

describe('跨库查询测试', () => {
  test('where', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'db native' }])

    // 前端调用
    const sql = await clientCloud.$use('test').findAll({
      where: { title: { $like: '%db native%' } },
      onlyGenerateSql: true
    })

    expect(sql).toEqual("SELECT * FROM `tag` WHERE `tag`.`title` LIKE '%db native%';")

    const res = await clientCloud.$use('test').findAll({
      where: { title: { $like: '%db native%' } }
    })

    expect(res.map(i => i.title)).toEqual(['db native'])
  })

  test('keyMap', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'keyMap' }])

    // 前端调用
    const res = await clientCloud.$use('test').findOne({
      where: { title: 'keyMap' }
    })

    expect(res.title).toEqual('keyMap')
  })

  test('order，group', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { title: 'order，group1', order: 1 },
      { title: 'order，group2', order: 2 },
      { title: 'order，group3', order: 3 },
      { title: 'order，group3', order: 3 }
    ])

    // 前端调用
    const res = await clientCloud.$use('test').findAll({
      attributes: [['fn("COUNT", col("id"))', 'count'], 'order'],
      where: { title: { $like: '%order，group%' } },
      order: [['order', 'ASC']],
      group: ['order']
    })

    expect(res).toEqual([
      { count: 1, order: 1 },
      { count: 1, order: 2 },
      { count: 2, order: 3 }
    ])
  })

  test('attributes（不支持 exclude）', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'abc', order: 1 }])

    // 前端调用
    const res = await clientCloud.$use('test').findAll({
      where: { title: 'abc' },
      attributes: ['id', 'title', 'color']
    })

    expect(_.keys(res[0]).sort()).toEqual(['color', 'id', 'title'])
  })

  test('attributes（fn，col，literal）', async () => {
    const { clientCloud } = await init()

    // 前端调用
    const sql = await clientCloud.$use('test').findAll({
      where: { title: 'abc' },
      attributes: [
        [`fn('SUM', col('order'))`, 'sum'],
        [`fn('COUNT', col('id'))`, 'id']
      ],
      onlyGenerateSql: true
    })

    expect(sql).toEqual("SELECT SUM(`order`) AS `sum`, COUNT(`id`) AS `id` FROM `tag` WHERE `tag`.`title` = 'abc';")
  })

  test('findOne()', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'findOne' }])

    // 前端调用
    const res = await clientCloud.$use('test').findOne({
      where: { title: 'findOne' }
    })

    expect(res.title).toEqual('findOne')
  })

  test('findAll()', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'findAll' }])

    // 前端调用
    const res = await clientCloud.$use('test').findAll({
      where: { title: 'findAll' }
    })
    expect(res.map(i => i.title)).toEqual(['findAll'])
  })

  test('findAndCountAll()', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'findAndCountAll' }])

    // 前端调用
    const res = await clientCloud.$use('test').findAndCountAll({
      where: { title: 'findAndCountAll' }
    })
    expect(res.list.map(i => i.title)).toEqual(['findAndCountAll'])
    expect(res.total).toEqual(1)
  })

  test('count()', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'count' }])

    // 前端调用
    const res = await clientCloud.$use('test').count({
      where: { title: 'count' }
    })

    expect(res).toEqual(1)
  })

  test('count(): group', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([
      { title: 'count1', order: 1 },
      { title: 'count2', order: 1 },
      { title: 'count3', order: 2 },
      { title: 'count4', order: 3 }
    ])

    // 前端调用
    const res = await clientCloud.$use('test').count<any>({
      attributes: ['title'],
      where: { title: { $like: '%count%' } },
      group: ['order']
    })

    expect(res.length).toEqual(3)
  })

  test('convertJsonField', async () => {
    const { clientCloud } = await init()

    await clientCloud.Tag.bulkCreate([{ title: 'convertJsonField', config: { status: 1 }, userIds: ['a', 'b'] }])

    // 前端调用
    const res = await clientCloud.$use('test').findOne({
      where: { title: 'convertJsonField' },
      convertJsonField: ['config', 'userIds']
    })

    expect(res.config).toEqual({ status: 1 })
    expect(res.userIds).toEqual(['a', 'b'])
  })

  test('查询参数 query', async () => {
    const { clientCloud } = await init()

    // ?cloud?$.queryData&title=abc
    const res = await clientCloud.$execute('sumTest', [1, 2, 3], {
      query: { title: 'abc' }
    })
    expect(res).toEqual([6, { title: 'abc' }])
  })
})

describe('加密与解密测试', () => {
  test('前端请求加密', async () => {
    const cloud = createCloudService({
      baseUrl: '/test',
      entityMap: {
        Tag: 'Tag'
      },
      enableEncryption: true,
      microApp: 'test',
      umiRequest: (url, options) => {
        const body = options.body ? JSON.parse(options.body) : options.data || {}
        expect(body.params).toEqual('N4Ig7gFgpgTlIC5QEsAmiCMBfLQ')
        return { success: true }
      }
    })

    cloud.Tag.findAll({
      where: { id: 1 }
    })
  })

  // test('后端请求解密', async () => {
  //   const server = new CloudServer({
  //     sequelize: null
  //   })
  //   const cloud = createCloudService({
  //     baseUrl: '/test',
  //     entityMap: {
  //       Tag: 'Tag'
  //     },
  //     enableEncryption: true,
  //     microApp: 'test',
  //     umiRequest: (url, options) => {
  //       expect(options.data.params).toEqual('N4Ig7gFgpgTlIC5QEsAmiCMBfLQ')
  //       const newData = CloudServer.decryptBody({ 'x-encryption': 'L1' }, options.data)
  //       expect(newData).toEqual({
  //         functionName: 'findAll',
  //         params: { where: { id: 1 } },
  //         modelName: 'Tag',
  //         data: undefined
  //       })
  //       return { success: true }
  //     }
  //   })
  //   cloud.Tag.findAll({
  //     where: { id: 1 }
  //   })
  // })
})

describe('测试云参数转换函数', () => {
  test('cloudParamsToNative', () => {
    const query = cloudParamsToNative<{
      id: string
      name: string
      title: string
    }>({
      where: {
        id: { $in: ['a', 'b'] }
      },
      attributes: ['id', 'title', 'name'],
      offset: 0,
      limit: 12,
      order: [['id', 'DESC']]
    })

    expect(query.limit).toEqual(12)
    expect(query.attributes).toEqual(['id', 'title', 'name'])
  })

  test('generateDataPermissionWhere', () => {
    const where = generateDataPermissionWhere({
      id: { $in: ['a', 'b'] }
    }, {
      dbType: 'pg',
      userId: '1',
      modelName: 'Tag',
      permissionTableName: 'sugo_data_permission',
      type: 'abiThemeAnalysis',
      adminType: 'abiThemeAnalysis',
      targetFieldName: 'themeId',
      createdByFieldName: 'createdBy'
    })
    // 断言where对象结构及SQL片段引号无重复
    const andSymbol = Object.getOwnPropertySymbols(where).find(s => s.toString() === 'Symbol(and)')
    expect(andSymbol).toBeDefined()
    const andArr = where[andSymbol]
    expect(Array.isArray(andArr)).toBe(true)
    // id字段断言
    expect(andArr[0]).toHaveProperty('id')
    // Literal对象SQL片段断言
    const literal = andArr.find(item => item && item.val)
    expect(literal).toBeDefined()
    // 检查SQL片段字段名引号只包裹一次
    const sql = literal.val
    // 断言字段名如 "Tag"."createdBy" 只包裹一层引号
    expect(sql).toMatch(/"Tag"\."createdBy"/)
    expect(sql).toMatch(/"Tag"\."themeId"/)
    // 不应出现重复引号
    expect(sql).not.toMatch(/""Tag""/)
    expect(sql).not.toMatch(/""createdBy""/)
    // 幂等性测试：多次调用wrapColName不会重复加引号
    const { wrapColName } = require('../server/src/utils')
    const col = 'Tag.createdBy'
    const once = wrapColName('postgres', col)
    const twice = wrapColName('postgres', once)
    expect(once).toBe('"Tag"."createdBy"')
    expect(twice).toBe('"Tag"."createdBy"')
  })
})

describe('测试权限功能', () => {
  test('dataPermission 内置的云函数', async () => {
    const { clientCloud } = await init()
    const res = await clientCloud.$fn.getDataPermission({})

    expect(res).toEqual({ isAdmin: false })
  })

  test('自定义云函数获取 dataPermission', async () => {
    const { clientCloud, cloudServer } = await init()

    clientCloud.Tag.findAll({
      headers: {
        'x-auth': 'xxx'
      }
    })

    cloudServer.register('test1', async (_request, context) => {
      const { dataPermission } = context

      const isAdmin = await dataPermission.get('isAdmin')
      return isAdmin
    })

    const res = await clientCloud.$execute('test1', {}, {})
    expect(res).toEqual(false)
  })

  test('支持模糊查询', async () => {
    const { clientCloud } = await init()
    const res = await clientCloud.$fn.getDataPermission({
      type: 'abi*'
    })

    expect(res).toEqual({ isAdmin: false })
  })
})

describe('加密功能', () => {
  test('加密 L1', async () => {
    const { clientCloud } = await init({
      enableEncryption: 'L1'
    })
    await clientCloud.Tag.bulkCreate([
      { title: 'count1', order: 1 },
      { title: 'count2', order: 1 },
      { title: 'count3', order: 2 },
      { title: 'count4', order: 3 }
    ])

    const res = await clientCloud.Tag.findAll({})

    expect(res.length).toEqual(4)
  })

  test('加密 L2', async () => {
    const { clientCloud } = await init({
      enableEncryption: 'L2'
    })
    await clientCloud.Tag.bulkCreate([
      { title: 'count1', order: 1 },
      { title: 'count2', order: 1 },
      { title: 'count3', order: 2 },
      { title: 'count4', order: 3 }
    ])

    const res = await clientCloud.Tag.findAll({})

    expect(res.length).toEqual(4)
  })

  test('加密 L3', async () => {
    const { clientCloud } = await init({
      enableEncryption: 'L3'
    })
    await clientCloud.Tag.bulkCreate([
      { title: 'count1', order: 1 },
      { title: 'count2', order: 1 },
      { title: 'count3', order: 2 },
      { title: 'count4', order: 3 }
    ])

    const res = await clientCloud.Tag.findAll({})

    expect(res.length).toEqual(4)
  })
})
