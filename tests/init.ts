import { Sequelize, INTEGER, STRING, DATE, JSON as TJ<PERSON><PERSON>, ModelC<PERSON> } from 'sequelize'
import { join } from 'path'
import fs from 'fs'
import _ from 'lodash'

import { createCloudService } from '../client/src'
import { CloudServer } from '../server/src'

import type { CloudFunction } from '../server/src/type'
import type { Entity, Tag, Funt } from './type'

let firsSequelizetCheck = false

// 重置数据库
fs.writeFileSync(join(__dirname, './db.sqlite'), '')

// 初始化 sequelize
const sequelize = new Sequelize({
  dialect: 'sqlite', // 数据库类型
  storage: join(__dirname, './db.sqlite'),  // 数据库文件的路径和名称
  logging: false
})

// 设置表结构
const Tag = sequelize.define('tag', {
  id: {
    type: STRING,
    primaryKey: true,
    defaultValue: () => Math.random().toString(32).slice(2),
  },
  title: { type: STRING, allowNull: false, comment: '名称' },
  color: { type: STRING, defaultValue: '#333', comment: '颜色' },
  order: { type: INTEGER, defaultValue: 0, comment: '排序' },
  parentId: { type: STRING, comment: '父节点 id' },
  config: { type: TJSON, comment: '配置信息', defaultValue: {} },
  userIds: { type: TJSON, comment: '用户 IDs', defaultValue: [] },
  createdAt: { type: DATE, defaultValue: () => new Date() },
  updatedAt: { type: DATE, defaultValue: () => new Date() },
}, {
  freezeTableName: true,
  comment: '标签表',
  underscored: true
})

// 设置关联父节点
Tag.belongsTo(Tag, { as: 'parent', foreignKey: 'parentId', constraints: false })

// 初始化服务
export async function init(clientOpts?: any) {
  // 同步创建表
  if (!firsSequelizetCheck) {
    await sequelize.authenticate()
    await sequelize.sync({ force: true })
  }

  // 模拟创建一个后端服务
  const cloudServer = new CloudServer({
    sequelize: sequelize,
    entityMap: { Tag },
    logging: false,
    redis: {
      get: k => {
        return (JSON as any).stringify({ indice_record: [1, 2, 3] })
      },
    },
    dbConfig: {
      // allowAnyDbQuery: true,
      allowOnlyGenerateSql: true,
      keyMap: {
        test: { dbName: 'test', tableName: 'tag' }
      }
    },
    // 注册云函数
    registerFunction: register => {
      const sumTest: CloudFunction = request => {
        return [_.sum(request.params), request.query]
      }
      register('sumTest', sumTest)
    }
  })

  // 模拟创建一个前端服务
  const clientCloud = createCloudService<{ Tag: any }>({
    ...clientOpts,
    microApp: 'test',
    baseUrl: '/test',
    entityMap: {
      Tag: 'Tag'
    },
    umiRequest: async (url: string, options: any) => {
      try {
        const body = options.body ? JSON.parse(options.body) : options.data || {}
        const res = await cloudServer.query({
          body,
          headers: options.headers,
          query: options.params
        })
        return res
      } catch (err) {
        console.error(err)
        throw err
      }
    }
  })

  return {
    sequelize,
    clientCloud,
    cloudServer
  }
}

interface Func {
  (name: 'sum' | 'avg' | 'sumTest', params: any): any
  avg: () => any
}

// 定义云函数
export const funcMap: Record<string, CloudFunction<{
  Params: any,
  Sequelize: Sequelize,
  Entitys: {
    Tag: ModelCtor<any>
  },
  Plugin: {
    hello?: () => number
  },
  Execute: Func
}>> = {
  sum: (request) => {
    return _.sum(request.params || [])
  },
  sumTest: (request, content) => {
    return [_.sum(request.params), request.query]
  },
  avg: (request) => {
    return _.floor(_.sum(request.params || []) / _.size(request.params || []), 0)
  },
  transaction: async (request, context) => {
    const { sequelize, db } = context

    try {
      await sequelize.transaction(async transaction => {
        await db.Tag.create({ title: 'transaction' }, { transaction })
        throw new Error('xxx') // 抛出一个错误让它自动回滚
      })
    } catch (err) {
      // ...
    }

    return true
  },
  inside: async (request, context) => {
    const { $execute, $fn } = context
    const arr = request.params || []
    const sum = await $execute('sum', arr)
    const avg = await $execute('avg', arr)
    const sum2 = await $fn.sum(arr)
    const avg2 = await $fn.avg(arr)

    return {
      sum, avg,
      sum2, avg2
    }
  },
  inside2: async (request, context) => {
    const { $execute, $fn } = context
    const arr = request.params || []
    const a = await $execute('inside', arr)
    const b = await $fn.inside(arr)

    return a.sum + b.sum
  },
  plugin: (request, context) => {
    const { plugin } = context
    return plugin.hello()
  }
}
