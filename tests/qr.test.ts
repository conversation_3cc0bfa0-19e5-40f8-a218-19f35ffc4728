import crypto, { encrypt, decrypt } from '../server/src/encrypt'

describe('Uint16Array 版加密解密 (TypeScript)', () => {
  test('加密解密 ASCII 字符', () => {
    const original = "abc123"
    const n = 5
    expect(decrypt(encrypt(original, n), n)).toBe(original)
  })

  test('处理编码边界值 (0x0000 和 0xFFFF)', () => {
    const original = String.fromCharCode(0, 0xFFFF)
    const n = 10
    expect(decrypt(encrypt(original, n), n)).toBe(original)
  })

  test('加密解密中文字符 (BMP 范围)', () => {
    const original = "中文测试"
    const n = 3
    expect(decrypt(encrypt(original, n), n)).toBe(original)
  })

  test('处理大 n 值', () => {
    const original = "test"
    const n = 100000
    expect(decrypt(encrypt(original, n), n)).toBe(original)
  })

  test('处理负 n 值', () => {
    const original = "hello"
    const n = -3
    expect(decrypt(encrypt(original, n), n)).toBe(original)
  })

  test('空字符串处理', () => {
    expect(encrypt("", 10)).toBe("")
    expect(decrypt("", 10)).toBe("")
  })

  test('不处理四字节字符 (明确预期成功)', () => {
    const original = "𠮷" // 四字节字符
    const n = 2
    expect(decrypt(encrypt(original, n), n)).toBe(original)
  })
})


describe('加密策略 T1', () => {
  test('正常压缩解压', () => {
    const original = "Hello, 你好！123"
    const encrypted = crypto.T1.encrypt(original)
    const decrypted = crypto.T1.decrypt(encrypted)
    expect(decrypted).toBe(original)
  })

  test('空字符串处理', () => {
    const original = ""
    const encrypted = crypto.T1.encrypt(original)
    const decrypted = crypto.T1.decrypt(encrypted)
    expect(decrypted).toBe(original)
  })

  test('特殊字符处理 (URL安全)', () => {
    const original = "!@#$%^&*()_+{}|:<>?"
    const encrypted = crypto.T1.encrypt(original)
    const decrypted = crypto.T1.decrypt(encrypted)
    expect(decrypted).toBe(original)
  })
})

describe('加密策略 T2', () => {
  const sign = "SUGO0008"

  test('正常加密解密', () => {
    const original = "Hello, 你好！123"
    const encrypted = crypto.T2.encrypt(original, sign)
    const decrypted = crypto.T2.decrypt(encrypted, sign)
    expect(decrypted).toBe(original)
  })

  test('加密后格式验证', () => {
    const encrypted = crypto.T2.encrypt("test", sign)
    // 验证格式是否为 "加密内容#签名"
    expect(encrypted).toMatch(new RegExp(`#T${sign}$`))
  })

  test('特殊字符处理 (URL安全)', () => {
    const original = "!@#$%^&*()_+{}|:<>?"
    const encrypted = crypto.T2.encrypt(original, sign)
    const decrypted = crypto.T2.decrypt(encrypted, sign)
    expect(decrypted).toBe(original)
  })

  test('大文本性能测试', () => {
    const original = "a".repeat(10000) // 10k 字符
    const encrypted = crypto.T2.encrypt(original, sign)
    const decrypted = crypto.T2.decrypt(encrypted, sign)
    expect(decrypted).toBe(original)
  })
})

describe('加密策略 S2', () => {
  const timestamp = Date.now() // 固定时间戳，最后四位是 5123

  // 测试 1: 正常对象加密解密
  test('加密对象后能正确解密', () => {
    const original = { name: "Alice", age: 30, list: [...new Array(19)].map(i => Math.random()) }
    const encrypted = crypto.S2.encrypt(original, timestamp)
    const decrypted = crypto.S2.decrypt(encrypted)
    expect(decrypted).toEqual(original)
  })

  // 测试 2: 验证签名格式
  test('加密字符串包含正确签名格式', () => {
    const encrypted = crypto.S2.encrypt({ data: "test" }, timestamp)
    expect(encrypted).toMatch(/#T\d{4}$/) // 格式如 #T5123
  })

  // 测试 4: 处理非JSON数据
  test('解密非JSON字符串返回原始内容', () => {
    const original = "plain-text"
    const encrypted = crypto.S2.encrypt(original, timestamp)
    const decrypted = crypto.S2.decrypt(encrypted)
    expect(decrypted).toBe(original)
  })

  // 测试 5: 空对象处理
  test('加密空对象后能正确解密', () => {
    const original = {}
    const encrypted = crypto.S2.encrypt(original, timestamp)
    const decrypted = crypto.S2.decrypt(encrypted)
    expect(decrypted).toEqual(original)
  })

  // 测试 7: 大对象性能测试
  test('处理10KB数据无异常', () => {
    const bigData = { data: "a".repeat(10240) } // 10KB 数据
    const encrypted = crypto.S2.encrypt(bigData, timestamp)
    const decrypted = crypto.S2.decrypt(encrypted)
    expect(decrypted.data.length).toBe(10240)
  })

  // 测试 8: 特殊字符处理
  test('加密含特殊字符的JSON', () => {
    const original = { chars: "!@#$%^&*()_+{}|:<>?" }
    const encrypted = crypto.S2.encrypt(original, timestamp)
    const decrypted = crypto.S2.decrypt(encrypted)
    expect(decrypted).toEqual(original)
  })
})
