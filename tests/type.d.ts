
export interface BaseModel {
  id: string
  createdAt?: Date | string
}

export interface Tag extends BaseModel {
  title: string
  color?: string
  order?: number
  parentId?: string
  parent?: Tag
  config?: Record<string, any>
  userIds?: string[]
}

export interface Entity {
  Tag: Tag
}

/**
 * 云函数类型定义
 * key: functionName
 * value: params
 */
export interface Funt {
  sum: (params: number[]) => number[],
  sumTest: number[],
  add: [number, number],
  transaction: Record<string, string>,
  inside: number[],
  plugin: any
  getDataPermission: (params: { type?: string }) => any
}
