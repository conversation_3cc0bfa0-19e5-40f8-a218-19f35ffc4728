{"name": "@sugo/sequelize-cloud", "version": "2.13.3", "description": "- 完备的 Typescript 支持，让你轻松借助类型智能来编写代码；\r - 去 controller、service、api 化，不需要关心这些，没有复杂的概念；\r - 只需要定义表结构，一切可在前端通过函数式调用；\r - 支持请求鉴权与加密；\r - 支持 json 字段级的查询；", "main": "index.js", "scripts": {"i": "npm i --no-audit --no-fund && (cd client &&  npm i --no-audit --no-fund) && (cd server && npm i --no-audit --no-fund) ", "test": "jest", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "build": "npm test && npm run build:client && npm run build:server", "prerelease": "npm run build", "release": "npm publish --registry=http://*************:4873/"}, "dependencies": {"cookie": "^0.5.0", "is-json": "^2.0.1", "lodash": "^4.17.21", "lz-string": "^1.5.0", "nanoid": "^3.3.4"}, "author": "lizhooh <<EMAIL>>", "files": ["client", "server"], "license": "ISC", "devDependencies": {"@babel/runtime": "^7.22.15", "@sequelize/sqlite3": "^7.0.0-alpha.41", "@types/jest": "^29.5.3", "@types/lodash": "^4.14.195", "@types/node": "^20.4.1", "jest": "^29.6.1", "sequelize": "^6.32.1", "sqlite3": "^5.1.6", "ts-jest": "^29.1.1", "typescript": "^5.1.6"}}